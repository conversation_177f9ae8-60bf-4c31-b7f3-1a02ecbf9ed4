import Phaser from 'phaser';
import { NotificationManager } from '../managers/NotificationManager.js';

/**
 * 启动场景
 * 负责预加载游戏资源和初始化
 */
export class BootScene extends Phaser.Scene {
  constructor() {
    super({ key: 'BootScene' });
  }

  preload() {
    // 创建加载进度条
    this.createLoadingBar();

    // 这里将来会加载游戏资源
    // 目前先模拟加载过程
    this.load.image(
      'placeholder',
      'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    );
  }

  create() {
    console.log('BootScene: 资源加载完成');

    // 初始化通知管理器
    this.notificationManager = new NotificationManager(this);

    // 显示欢迎消息
    this.notificationManager.showInfo('游戏加载完成！', 2000);

    // 启动主菜单场景
    this.scene.start('MenuScene');
  }

  createLoadingBar() {
    const width = this.cameras.main.width;
    const height = this.cameras.main.height;

    // 加载文本
    const loadingText = this.add
      .text(width / 2, height / 2 - 50, '加载中...', {
        fontSize: '24px',
        fill: '#ffffff',
      })
      .setOrigin(0.5);

    // 进度条背景
    const progressBg = this.add.graphics();
    progressBg.fillStyle(0x222222);
    progressBg.fillRect(width / 2 - 150, height / 2, 300, 20);

    // 进度条
    const progressBar = this.add.graphics();

    // 监听加载进度
    this.load.on('progress', value => {
      progressBar.clear();
      progressBar.fillStyle(0x4caf50);
      progressBar.fillRect(width / 2 - 150, height / 2, 300 * value, 20);
    });

    // 加载完成
    this.load.on('complete', () => {
      loadingText.setText('加载完成!');
      setTimeout(() => {
        progressBg.destroy();
        progressBar.destroy();
        loadingText.destroy();
      }, 500);
    });
  }
}
