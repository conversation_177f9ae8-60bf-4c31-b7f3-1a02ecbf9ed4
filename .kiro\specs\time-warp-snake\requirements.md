# Requirements Document

## Introduction

时空蛇是一个创新的商用级移动端贪吃蛇游戏，核心特色是时空倒流机制。游戏基于 Phaser 3 引擎开发，专为触控设备优化，包含元素系统、多种食物类型、皮肤系统和商业化功能。游戏支持 PWA 安装，具有现代化的粒子效果和程序化音效系统。

## Requirements

### Requirement 1

**User Story:** 作为玩家，我希望能够通过触控操作控制蛇的移动，以便在移动设备上流畅地游玩

#### Acceptance Criteria

1. WHEN 玩家在屏幕上向上滑动 THEN 蛇 SHALL 向上移动
2. WHEN 玩家在屏幕上向下滑动 THEN 蛇 SHALL 向下移动
3. WHEN 玩家在屏幕上向左滑动 THEN 蛇 SHALL 向左移动
4. WHEN 玩家在屏幕上向右滑动 THEN 蛇 SHALL 向右移动
5. WHEN 蛇正在向某个方向移动 THEN 系统 SHALL 阻止蛇立即反向移动
6. WHEN 滑动距离小于最小阈值 THEN 系统 SHALL 忽略该滑动操作

### Requirement 2

**User Story:** 作为玩家，我希望能够使用时空倒流功能避免碰撞，以便增加游戏的策略性和容错性

#### Acceptance Criteria

1. WHEN 玩家点击屏幕 AND 时空能量充足 THEN 蛇 SHALL 回到几步之前的位置
2. WHEN 使用时空倒流 THEN 系统 SHALL 消耗相应的时空能量
3. WHEN 时空能量不足 THEN 系统 SHALL 禁用时空倒流功能
4. WHEN 时空倒流激活 THEN 系统 SHALL 显示倒流特效
5. IF 蛇的历史位置少于倒流步数 THEN 系统 SHALL 回到最早的可用位置

### Requirement 3

**User Story:** 作为玩家，我希望能够收集不同类型的食物获得不同效果，以便让游戏更加丰富有趣

#### Acceptance Criteria

1. WHEN 蛇吃到普通食物 THEN 系统 SHALL 增加 10 分并让蛇身增长 1 节
2. WHEN 蛇吃到火元素食物 THEN 系统 SHALL 增加 20 分、让蛇身增长 1 节并添加火焰效果
3. WHEN 蛇吃到水元素食物 THEN 系统 SHALL 增加 20 分、让蛇身增长 1 节并添加水流效果
4. WHEN 蛇吃到电元素食物 THEN 系统 SHALL 增加 20 分、让蛇身增长 1 节并添加电光效果
5. WHEN 蛇吃到时空食物 THEN 系统 SHALL 增加 30 分、让蛇身增长 1 节并恢复时空能量
6. WHEN 蛇吃到黄金食物 THEN 系统 SHALL 增加 50 分、让蛇身增长 1 节并临时提升移动速度
7. WHEN 食物被吃掉 THEN 系统 SHALL 在随机位置生成新的食物

### Requirement 4

**User Story:** 作为玩家，我希望游戏能够检测碰撞并结束游戏，以便维持游戏的挑战性

#### Acceptance Criteria

1. WHEN 蛇头撞到游戏边界 THEN 系统 SHALL 结束游戏
2. WHEN 蛇头撞到自己的身体 THEN 系统 SHALL 结束游戏
3. WHEN 游戏结束 THEN 系统 SHALL 显示游戏结束界面
4. WHEN 游戏结束 THEN 系统 SHALL 保存最高分记录
5. WHEN 游戏结束 THEN 系统 SHALL 播放游戏结束音效

### Requirement 5

**User Story:** 作为玩家，我希望能够购买和使用不同的蛇皮肤，以便个性化我的游戏体验

#### Acceptance Criteria

1. WHEN 玩家打开皮肤商店 THEN 系统 SHALL 显示所有可用皮肤及其价格
2. WHEN 玩家购买皮肤 AND 金币充足 THEN 系统 SHALL 扣除金币并解锁皮肤
3. WHEN 玩家选择已解锁的皮肤 THEN 系统 SHALL 应用该皮肤到游戏中
4. WHEN 玩家金币不足 THEN 系统 SHALL 显示金币不足提示
5. IF 皮肤已解锁 THEN 系统 SHALL 显示"已拥有"状态
6. WHEN 应用皮肤 THEN 系统 SHALL 使用程序化生成的颜色和效果（无需图片素材）

### Requirement 6

**User Story:** 作为玩家，我希望能够购买和使用游戏道具，以便在游戏中获得优势

#### Acceptance Criteria

1. WHEN 玩家使用双倍得分道具 THEN 系统 SHALL 在限定时间内将所有得分翻倍
2. WHEN 玩家使用时空增强道具 THEN 系统 SHALL 增加时空能量上限和恢复速度
3. WHEN 玩家使用磁性食物道具 THEN 系统 SHALL 让食物自动向蛇移动
4. WHEN 玩家使用护盾保护道具 THEN 系统 SHALL 在下次碰撞时保护蛇不死亡
5. WHEN 道具效果结束 THEN 系统 SHALL 显示道具失效提示

### Requirement 7

**User Story:** 作为玩家，我希望游戏具有丰富的视觉和音效效果，以便获得沉浸式的游戏体验

#### Acceptance Criteria

1. WHEN 蛇移动时 THEN 系统 SHALL 显示相应的元素轨迹效果
2. WHEN 蛇吃到食物时 THEN 系统 SHALL 播放吃食物音效并显示粒子效果
3. WHEN 使用时空倒流时 THEN 系统 SHALL 播放时空音效并显示时空扭曲效果
4. WHEN 游戏进行时 THEN 系统 SHALL 播放背景音乐
5. WHEN 玩家调整音效设置 THEN 系统 SHALL 相应调整音效和音乐音量
6. WHEN 渲染蛇身 THEN 系统 SHALL 使用方块形状绘制蛇身节点
7. WHEN 渲染蛇头 THEN 系统 SHALL 使用 Canvas 绘制眼睛和鼻子细节

### Requirement 8

**User Story:** 作为玩家，我希望游戏能够适配不同的移动设备，以便在各种设备上都能正常游玩

#### Acceptance Criteria

1. WHEN 游戏在不同屏幕尺寸设备上运行 THEN 系统 SHALL 自动调整游戏界面布局
2. WHEN 设备方向改变 THEN 系统 SHALL 适配新的屏幕方向
3. WHEN 玩家尝试缩放页面 THEN 系统 SHALL 阻止缩放操作
4. WHEN 支持振动的设备上发生碰撞 THEN 系统 SHALL 触发设备振动
5. IF 设备支持 PWA THEN 系统 SHALL 允许用户安装到主屏幕

### Requirement 9

**User Story:** 作为玩家，我希望能够通过游戏内货币系统购买物品，以便享受完整的游戏体验

#### Acceptance Criteria

1. WHEN 玩家完成游戏 THEN 系统 SHALL 根据分数奖励相应金币
2. WHEN 玩家观看广告 THEN 系统 SHALL 奖励额外金币
3. WHEN 玩家进行内购 THEN 系统 SHALL 增加相应数量的金币
4. WHEN 玩家购买物品 THEN 系统 SHALL 扣除相应金币
5. WHEN 金币数量变化 THEN 系统 SHALL 更新界面显示

### Requirement 10

**User Story:** 作为玩家，我希望游戏具有菜单系统和设置功能，以便管理游戏偏好和查看信息

#### Acceptance Criteria

1. WHEN 游戏启动 THEN 系统 SHALL 显示主菜单界面
2. WHEN 玩家点击开始游戏 THEN 系统 SHALL 进入游戏场景
3. WHEN 玩家打开设置 THEN 系统 SHALL 显示音效、音乐、振动等设置选项
4. WHEN 玩家调整设置 THEN 系统 SHALL 保存设置并立即生效
5. WHEN 玩家查看最高分 THEN 系统 SHALL 显示历史最高分记录
