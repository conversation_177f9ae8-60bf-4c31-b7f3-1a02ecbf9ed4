<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover" />
  <meta name="description" content="时空蛇 - 创新的移动端贪吃蛇游戏，核心特色是时空倒流机制" />
  <meta name="keywords" content="贪吃蛇,游戏,移动端,时空倒流,Phaser" />
  <meta name="author" content="Time Warp Snake Team" />
  
  <!-- PWA Meta Tags -->
  <meta name="theme-color" content="#4CAF50" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="apple-mobile-web-app-title" content="时空蛇" />
  
  <!-- Icons -->
  <link rel="icon" type="image/svg+xml" href="/assets/icon.svg" />
  <link rel="apple-touch-icon" href="/assets/icon-192.png" />
  <link rel="manifest" href="/manifest.json" />
  
  <title>时空蛇 - Time Warp Snake</title>
  
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      overflow: hidden;
      touch-action: manipulation;
      user-select: none;
      -webkit-user-select: none;
      -webkit-touch-callout: none;
    }
    
    #game-container {
      width: 100vw;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    #loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 18px;
      text-align: center;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="game-container">
    <div id="loading">
      <div class="loading-spinner"></div>
      <div>加载中...</div>
    </div>
  </div>
  
  <script type="module" src="/src/main.js"></script>
</body>
</html>
