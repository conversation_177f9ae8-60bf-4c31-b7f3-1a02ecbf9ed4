/**
 * 游戏常量定义
 * 时空蛇游戏的所有常量配置
 */

// 游戏基础配置
export const GAME = {
  TITLE: '时空蛇',
  VERSION: '1.0.0',
  GRID_SIZE: 20,
  INITIAL_SNAKE_LENGTH: 3,
  INITIAL_SPEED: 150, // 毫秒
  MIN_SPEED: 80,
  MAX_SPEED: 300,
  SPEED_INCREMENT: 10
};

// 游戏状态枚举
export const GAME_STATES = {
  MENU: 'menu',
  PLAYING: 'playing',
  PAUSED: 'paused',
  GAME_OVER: 'game_over',
  SHOP: 'shop',
  SETTINGS: 'settings'
};

// 方向枚举
export const DIRECTIONS = {
  UP: 'up',
  DOWN: 'down',
  LEFT: 'left',
  RIGHT: 'right'
};

// 方向向量
export const DIRECTION_VECTORS = {
  [DIRECTIONS.UP]: { x: 0, y: -1 },
  [DIRECTIONS.DOWN]: { x: 0, y: 1 },
  [DIRECTIONS.LEFT]: { x: -1, y: 0 },
  [DIRECTIONS.RIGHT]: { x: 1, y: 0 }
};

// 食物类型配置
export const FOOD_TYPES = {
  NORMAL: {
    id: 'normal',
    emoji: '🍎',
    color: 0xFF0000,
    score: 10,
    effect: null,
    probability: 0.6,
    duration: 0
  },
  FIRE: {
    id: 'fire',
    emoji: '🍓',
    color: 0xFF4500,
    score: 20,
    effect: 'fire',
    probability: 0.15,
    duration: 5000
  },
  WATER: {
    id: 'water',
    emoji: '🍇',
    color: 0x0080FF,
    score: 20,
    effect: 'water',
    probability: 0.15,
    duration: 5000
  },
  ELECTRIC: {
    id: 'electric',
    emoji: '🍊',
    color: 0xFFD700,
    score: 20,
    effect: 'electric',
    probability: 0.15,
    duration: 5000
  },
  TIMEWARP: {
    id: 'timewarp',
    emoji: '🍑',
    color: 0x8A2BE2,
    score: 30,
    effect: 'timewarp',
    probability: 0.08,
    duration: 0
  },
  GOLDEN: {
    id: 'golden',
    emoji: '🍍',
    color: 0xFFD700,
    score: 50,
    effect: 'golden',
    probability: 0.05,
    duration: 8000
  }
};

// 元素效果配置
export const ELEMENT_EFFECTS = {
  FIRE: {
    name: '火焰',
    color: 0xFF5722,
    glowColor: 0xFF9800,
    trailColor: 0xFF6600,
    particleConfig: {
      scale: { start: 0.5, end: 0 },
      speed: { min: 50, max: 100 },
      lifespan: 600,
      tint: [0xff6600, 0xff0000, 0xffaa00],
      blendMode: 'ADD'
    }
  },
  WATER: {
    name: '水流',
    color: 0x2196F3,
    glowColor: 0x00BCD4,
    trailColor: 0x0066ff,
    particleConfig: {
      scale: { start: 0.3, end: 0 },
      speed: { min: 30, max: 80 },
      lifespan: 800,
      tint: [0x0066ff, 0x00aaff, 0x66ccff],
      alpha: { start: 0.8, end: 0 }
    }
  },
  ELECTRIC: {
    name: '电光',
    color: 0xFFD700,
    glowColor: 0x66FF66,
    trailColor: 0xffff00,
    particleConfig: {
      scale: { start: 0.4, end: 0 },
      speed: { min: 80, max: 150 },
      lifespan: 400,
      tint: [0xffff00, 0xffffff, 0x66ff66],
      blendMode: 'ADD'
    }
  }
};

// 皮肤配置
export const SKIN_CONFIG = {
  CLASSIC: {
    id: 'classic',
    name: '经典绿色',
    bodyColor: 0x4CAF50,
    headColor: 0x388E3C,
    eyeColor: 0x000000,
    price: 0,
    unlocked: true
  },
  FIRE: {
    id: 'fire',
    name: '火焰蛇',
    bodyColor: 0xFF5722,
    headColor: 0xD84315,
    eyeColor: 0xFFEB3B,
    glowColor: 0xFF9800,
    price: 500,
    unlocked: false
  },
  WATER: {
    id: 'water',
    name: '水流蛇',
    bodyColor: 0x2196F3,
    headColor: 0x1976D2,
    eyeColor: 0xE3F2FD,
    glowColor: 0x00BCD4,
    price: 500,
    unlocked: false
  },
  ELECTRIC: {
    id: 'electric',
    name: '电光蛇',
    bodyColor: 0xFFD700,
    headColor: 0xFFC107,
    eyeColor: 0x000000,
    glowColor: 0x66FF66,
    price: 750,
    unlocked: false
  },
  RAINBOW: {
    id: 'rainbow',
    name: '彩虹蛇',
    bodyColor: 0xFF69B4,
    headColor: 0xFF1493,
    eyeColor: 0xFFFFFF,
    glowColor: 0x9932CC,
    price: 1000,
    unlocked: false
  },
  GALAXY: {
    id: 'galaxy',
    name: '星河蛇',
    bodyColor: 0x4B0082,
    headColor: 0x2E0054,
    eyeColor: 0xFFFFFF,
    glowColor: 0x8A2BE2,
    price: 1500,
    unlocked: false
  }
};

// 道具配置
export const ITEM_CONFIG = {
  DOUBLE_SCORE: {
    id: 'doubleScore',
    name: '双倍得分',
    description: '在限定时间内获得双倍分数',
    price: 100,
    duration: 30000,
    icon: '⭐'
  },
  TIME_BOOST: {
    id: 'timeBoost',
    name: '时空增强',
    description: '增加时空能量上限和恢复速度',
    price: 150,
    duration: 60000,
    icon: '⚡'
  },
  MAGNET: {
    id: 'magnet',
    name: '磁性食物',
    description: '食物会自动向蛇移动',
    price: 200,
    duration: 45000,
    icon: '🧲'
  },
  SHIELD: {
    id: 'shield',
    name: '护盾保护',
    description: '下次碰撞时保护蛇不死亡',
    price: 250,
    duration: 0, // 一次性使用
    icon: '🛡️'
  }
};

// 时空倒流配置
export const TIMEWARP_CONFIG = {
  MAX_ENERGY: 100,
  REGEN_RATE: 1, // 每秒恢复的能量
  COST_PER_USE: 30,
  HISTORY_LENGTH: 10, // 保存的历史步数
  COOLDOWN: 1000, // 冷却时间（毫秒）
  REWIND_STEPS: 5 // 每次倒流的步数
};

// 输入配置
export const INPUT_CONFIG = {
  SWIPE_THRESHOLD: 50, // 滑动阈值（像素）
  TAP_THRESHOLD: 200, // 点击时间阈值（毫秒）
  DOUBLE_TAP_THRESHOLD: 300, // 双击时间阈值（毫秒）
  HOLD_THRESHOLD: 500 // 长按时间阈值（毫秒）
};

// 分数配置
export const SCORE_CONFIG = {
  NORMAL_FOOD: 10,
  ELEMENT_FOOD: 20,
  TIMEWARP_FOOD: 30,
  GOLDEN_FOOD: 50,
  LENGTH_BONUS: 5, // 每节蛇身的额外分数
  SPEED_BONUS_MULTIPLIER: 1.2, // 速度奖励倍数
  COMBO_MULTIPLIER: 1.5 // 连击倍数
};

// 金币配置
export const COIN_CONFIG = {
  SCORE_TO_COIN_RATIO: 10, // 10分数 = 1金币
  DAILY_BONUS: 100,
  AD_REWARD: 50,
  ACHIEVEMENT_REWARD: 200
};

// 音效配置
export const AUDIO_CONFIG = {
  MASTER_VOLUME: 1.0,
  MUSIC_VOLUME: 0.3,
  SFX_VOLUME: 0.7,
  FADE_DURATION: 1000
};

// 粒子效果配置
export const PARTICLE_CONFIG = {
  MAX_PARTICLES: 100,
  POOL_SIZE: 50,
  DEFAULT_LIFESPAN: 1000,
  QUALITY_LEVELS: {
    LOW: 0.3,
    MEDIUM: 0.6,
    HIGH: 1.0
  }
};

// 性能配置
export const PERFORMANCE_CONFIG = {
  TARGET_FPS: 60,
  MIN_FPS: 30,
  FRAME_HISTORY_SIZE: 10,
  QUALITY_CHECK_INTERVAL: 5000, // 5秒检查一次性能
  AUTO_QUALITY_ADJUSTMENT: true
};

// 本地存储键名
export const STORAGE_KEYS = {
  HIGH_SCORE: 'timeWarpSnake_highScore',
  COINS: 'timeWarpSnake_coins',
  SETTINGS: 'timeWarpSnake_settings',
  SKINS: 'timeWarpSnake_skins',
  ITEMS: 'timeWarpSnake_items',
  ACHIEVEMENTS: 'timeWarpSnake_achievements',
  GAME_DATA: 'timeWarpSnake_gameData'
};

// 事件名称
export const EVENTS = {
  SCORE_CHANGED: 'scoreChanged',
  COINS_CHANGED: 'coinsChanged',
  SKIN_CHANGED: 'skinChanged',
  ITEM_USED: 'itemUsed',
  ACHIEVEMENT_UNLOCKED: 'achievementUnlocked',
  GAME_OVER: 'gameOver',
  TIMEWARP_USED: 'timewarpUsed',
  FOOD_EATEN: 'foodEaten'
};
