// 菜单场景样式
.menu-scene {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  .center-flex();
  flex-direction: column;
  background: @bg-primary;
  padding: @spacing-lg;

  .menu-container {
    text-align: center;
    max-width: 400px;
    width: 100%;
    .center-flex();
    flex-direction: column;
  }
  
  .game-title {
    font-size: 3rem;
    font-weight: bold;
    color: @text-light;
    margin-bottom: @spacing-sm;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: titleGlow 3s ease-in-out infinite alternate;
    
    .mobile-only({
      font-size: 2.5rem;
    });
  }
  
  .game-subtitle {
    font-size: @font-size-lg;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: @spacing-xl;
    font-weight: 300;
  }
  
  .menu-buttons {
    display: flex;
    flex-direction: column;
    gap: @spacing-md;
    margin-bottom: @spacing-xl;
    
    .menu-btn {
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }
      
      &:hover::before {
        left: 100%;
      }
    }
  }
  

  
  .menu-footer {
    margin-top: auto;
    padding-top: @spacing-lg;
    
    .version-info {
      font-size: @font-size-xs;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: @spacing-sm;
    }
    
    .social-links {
      display: flex;
      justify-content: center;
      gap: @spacing-md;
      
      .social-link {
        color: rgba(255, 255, 255, 0.7);
        font-size: @font-size-lg;
        transition: all @transition-normal ease;
        
        &:hover {
          color: @text-light;
          transform: scale(1.2);
        }
      }
    }
  }
}

// 设置面板
.settings-panel {
  background: rgba(0, 0, 0, 0.8);
  border-radius: @border-radius-large;
  padding: @spacing-lg;
  backdrop-filter: blur(15px);
  max-width: 350px;
  width: 100%;
  
  .settings-title {
    font-size: @font-size-xl;
    font-weight: bold;
    color: @text-light;
    margin-bottom: @spacing-lg;
    text-align: center;
  }
  
  .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: @spacing-md;
    padding: @spacing-sm 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    
    .setting-label {
      color: @text-light;
      font-size: @font-size-md;
    }
    
    .setting-control {
      display: flex;
      align-items: center;
      gap: @spacing-sm;
    }
  }
  
  .volume-slider {
    width: 100px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    
    &::-webkit-slider-thumb {
      appearance: none;
      width: 16px;
      height: 16px;
      background: @primary-color;
      border-radius: 50%;
      cursor: pointer;
    }
    
    &::-moz-range-thumb {
      width: 16px;
      height: 16px;
      background: @primary-color;
      border-radius: 50%;
      cursor: pointer;
      border: none;
    }
  }
  
  .toggle-switch {
    position: relative;
    width: 50px;
    height: 24px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    cursor: pointer;
    transition: background @transition-normal ease;
    
    &.active {
      background: @primary-color;
    }
    
    &::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background: @text-light;
      border-radius: 50%;
      transition: transform @transition-normal ease;
    }
    
    &.active::after {
      transform: translateX(26px);
    }
  }
}

// 动画
@keyframes titleGlow {
  0% {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5), 0 0 20px rgba(76, 175, 80, 0.3);
  }
  100% {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5), 0 0 30px rgba(76, 175, 80, 0.6);
  }
}
