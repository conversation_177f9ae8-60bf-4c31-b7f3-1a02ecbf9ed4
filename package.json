{"name": "time-warp-snake", "private": true, "version": "1.0.0", "type": "module", "description": "创新的移动端贪吃蛇游戏，核心特色是时空倒流机制", "keywords": ["game", "snake", "mobile", "phaser", "pwa"], "author": "Time Warp Snake Team", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"phaser": "^3.70.0"}, "devDependencies": {"vite": "^5.0.8", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "prettier": "^3.1.1", "less": "^4.2.0"}}