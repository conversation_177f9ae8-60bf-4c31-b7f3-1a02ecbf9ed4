import Phaser from 'phaser';

/**
 * 商店场景
 * 皮肤和道具购买界面
 */
export class ShopScene extends Phaser.Scene {
  constructor() {
    super({ key: 'ShopScene' });
  }

  create() {
    console.log('ShopScene: 商店场景启动');

    const width = this.cameras.main.width;
    const height = this.cameras.main.height;

    // 商店标题
    this.add
      .text(width / 2, 80, '商店', {
        fontSize: '36px',
        fill: '#4CAF50',
      })
      .setOrigin(0.5);

    // 显示金币
    const coins = this.registry.get('coins') || 0;
    this.add
      .text(width / 2, 130, `金币: ${coins}`, {
        fontSize: '20px',
        fill: '#FFD700',
      })
      .setOrigin(0.5);

    // 临时显示文本
    this.add
      .text(width / 2, height / 2, '商店功能开发中...', {
        fontSize: '24px',
        fill: '#ffffff',
      })
      .setOrigin(0.5);

    // 返回菜单按钮
    const backButton = this.add
      .text(width / 2, height - 80, '返回菜单', {
        fontSize: '20px',
        fill: '#ffffff',
        backgroundColor: '#666666',
        padding: { x: 20, y: 10 },
      })
      .setOrigin(0.5)
      .setInteractive();

    backButton.on('pointerdown', () => {
      this.scene.start('MenuScene');
    });
  }
}
