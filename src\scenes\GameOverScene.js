import Phaser from 'phaser';

/**
 * 游戏结束场景
 * 游戏结束时显示分数和选项
 */
export class GameOverScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameOverScene' });
  }

  create() {
    console.log('GameOverScene: 游戏结束场景启动');

    const width = this.cameras.main.width;
    const height = this.cameras.main.height;

    // 半透明背景
    this.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.8);

    // 游戏结束文本
    this.add
      .text(width / 2, height / 2 - 100, '游戏结束', {
        fontSize: '36px',
        fill: '#FF5722',
      })
      .setOrigin(0.5);

    // 显示分数
    const score = this.registry.get('score') || 0;
    this.add
      .text(width / 2, height / 2 - 50, `得分: ${score}`, {
        fontSize: '24px',
        fill: '#ffffff',
      })
      .setOrigin(0.5);

    // 显示最高分
    const highScore = this.registry.get('highScore') || 0;
    if (score > highScore) {
      this.registry.set('highScore', score);
      this.add
        .text(width / 2, height / 2 - 20, '新纪录!', {
          fontSize: '20px',
          fill: '#4CAF50',
        })
        .setOrigin(0.5);
    } else {
      this.add
        .text(width / 2, height / 2 - 20, `最高分: ${highScore}`, {
          fontSize: '18px',
          fill: '#888888',
        })
        .setOrigin(0.5);
    }

    // 重新开始按钮
    const restartButton = this.add
      .text(width / 2, height / 2 + 30, '重新开始', {
        fontSize: '20px',
        fill: '#ffffff',
        backgroundColor: '#4CAF50',
        padding: { x: 20, y: 10 },
      })
      .setOrigin(0.5)
      .setInteractive();

    restartButton.on('pointerdown', () => {
      this.scene.start('GameScene');
    });

    // 返回菜单按钮
    const menuButton = this.add
      .text(width / 2, height / 2 + 90, '返回菜单', {
        fontSize: '20px',
        fill: '#ffffff',
        backgroundColor: '#666666',
        padding: { x: 20, y: 10 },
      })
      .setOrigin(0.5)
      .setInteractive();

    menuButton.on('pointerdown', () => {
      this.scene.start('MenuScene');
    });
  }
}
