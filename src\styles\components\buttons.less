// 基础按钮样式
.btn {
  .button-base();
  
  &.btn-primary {
    .button-primary();
  }
  
  &.btn-secondary {
    .button-secondary();
  }
  
  &.btn-success {
    background: @success-color;
    color: @text-light;
    
    &:hover:not(:disabled) {
      background: darken(@success-color, 10%);
    }
  }
  
  &.btn-warning {
    background: @warning-color;
    color: @text-primary;
    
    &:hover:not(:disabled) {
      background: darken(@warning-color, 10%);
    }
  }
  
  &.btn-danger {
    background: @danger-color;
    color: @text-light;
    
    &:hover:not(:disabled) {
      background: darken(@danger-color, 10%);
    }
  }
  
  // 按钮尺寸
  &.btn-sm {
    padding: @spacing-xs @spacing-sm;
    font-size: @font-size-sm;
  }
  
  &.btn-lg {
    padding: @spacing-md @spacing-xl;
    font-size: @font-size-lg;
  }
  
  // 圆形按钮
  &.btn-circle {
    border-radius: 50%;
    width: 50px;
    height: 50px;
    padding: 0;
  }
  
  // 轮廓按钮
  &.btn-outline {
    background: transparent;
    border: 2px solid @primary-color;
    color: @primary-color;
    
    &:hover:not(:disabled) {
      background: @primary-color;
      color: @text-light;
    }
  }
  
  // 图标按钮
  &.btn-icon {
    .center-flex();
    gap: @spacing-xs;
    
    .icon {
      font-size: 1.2em;
    }
  }
}

// 菜单按钮
.menu-btn {
  .btn;
  .btn-primary();
  width: 200px;
  margin: @spacing-sm 0;
  font-size: @font-size-lg;
  
  .mobile-only({
    width: 100%;
    max-width: 280px;
  });
}

// 游戏内按钮
.game-btn {
  .btn;
  background: rgba(0, 0, 0, 0.7);
  color: @text-light;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  
  &:hover:not(:disabled) {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(255, 255, 255, 0.5);
  }
  
  &.active {
    background: @primary-color;
    border-color: @primary-color;
  }
}

// 商店按钮
.shop-btn {
  .btn;
  .card();
  border: 2px solid transparent;
  transition: all @transition-normal ease;
  
  &:hover:not(:disabled) {
    border-color: @primary-color;
    transform: translateY(-4px);
  }
  
  &.purchased {
    background: @success-color;
    color: @text-light;
    
    &:hover {
      background: @success-color;
      transform: none;
    }
  }
  
  &.locked {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      border-color: transparent;
    }
  }
}

// 浮动操作按钮
.fab {
  position: fixed;
  bottom: @spacing-lg;
  right: @spacing-lg;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: @accent-color;
  color: @text-light;
  border: none;
  .center-flex();
  font-size: @font-size-lg;
  cursor: pointer;
  box-shadow: @shadow-lg;
  transition: all @transition-normal ease;
  z-index: @z-index-dropdown;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// 按钮组
.btn-group {
  display: flex;
  gap: @spacing-sm;
  
  &.btn-group-vertical {
    flex-direction: column;
  }
  
  &.btn-group-center {
    justify-content: center;
  }
  
  .btn {
    flex: 1;
    
    &:first-child {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    
    &:last-child {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
    
    &:not(:first-child):not(:last-child) {
      border-radius: 0;
    }
  }
  
  &.btn-group-vertical .btn {
    &:first-child {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      border-top-right-radius: @border-radius;
    }
    
    &:last-child {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      border-bottom-left-radius: @border-radius;
    }
  }
}
