import Phaser from 'phaser';
import { Snake } from '../entities/Snake.js';
import { GAME, DIRECTIONS } from '../config/Constants.js';

/**
 * 游戏逻辑场景
 * 只负责核心游戏逻辑，UI由HTML/CSS处理
 */
export class GameplayScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameplayScene' });

    // 游戏状态
    this.gameState = 'menu'; // menu, playing, paused, gameOver
    this.score = 0;
    this.timeWarpEnergy = 100;
    this.snake = null;
    this.food = null;

    // 输入相关
    this.inputSystem = null;
    this.lastSwipeTime = 0;
    this.swipeThreshold = 50;
  }

  create() {
    console.log('GameplayScene: 游戏逻辑场景启动');

    // 设置游戏世界边界
    this.setupGameWorld();

    // 初始化输入系统
    this.setupInputSystem();

    // 监听UI事件
    this.setupUIEventListeners();

    // 启动菜单状态
    this.switchToMenu();
  }

  /**
   * 设置游戏世界
   */
  setupGameWorld() {
    // 设置游戏区域大小
    this.gameWidth = this.cameras.main.width;
    this.gameHeight = this.cameras.main.height;

    // 计算网格数量
    this.gridWidth = Math.floor(this.gameWidth / GAME.GRID_SIZE);
    this.gridHeight = Math.floor(this.gameHeight / GAME.GRID_SIZE);

    console.log(
      `游戏区域: ${this.gameWidth}x${this.gameHeight}, 网格: ${this.gridWidth}x${this.gridHeight}`
    );
  }

  /**
   * 设置输入系统
   */
  setupInputSystem() {
    // 键盘输入
    this.cursors = this.input.keyboard.createCursorKeys();
    this.wasd = this.input.keyboard.addKeys('W,S,A,D');
    this.spaceKey = this.input.keyboard.addKey(
      Phaser.Input.Keyboard.KeyCodes.SPACE
    );

    // 触控输入
    this.input.on('pointerdown', this.handlePointerDown, this);
    this.input.on('pointerup', this.handlePointerUp, this);

    // 阻止默认的触控行为（检查是否存在）
    if (this.input.touch) {
      this.input.touch.capture = true;
    }
  }

  /**
   * 设置UI事件监听
   */
  setupUIEventListeners() {
    // 监听来自UI的事件
    window.addEventListener('menuAction', event => {
      this.handleMenuAction(event.detail.action);
    });

    window.addEventListener('gameAction', event => {
      this.handleGameAction(event.detail.action);
    });

    window.addEventListener('pauseAction', event => {
      this.handlePauseAction(event.detail.action);
    });

    window.addEventListener('gameOverAction', event => {
      this.handleGameOverAction(event.detail.action);
    });
  }

  /**
   * 处理菜单操作
   */
  handleMenuAction(action) {
    switch (action) {
      case 'startGame':
        this.startNewGame();
        break;
      case 'showShop':
        this.switchToShop();
        break;
      case 'showSettings':
        this.switchToSettings();
        break;
    }
  }

  /**
   * 处理游戏操作
   */
  handleGameAction(action) {
    switch (action) {
      case 'pauseGame':
        this.pauseGame();
        break;
      case 'useTimewarp':
        this.useTimewarp();
        break;
    }
  }

  /**
   * 处理暂停操作
   */
  handlePauseAction(action) {
    switch (action) {
      case 'resumeGame':
        this.resumeGame();
        break;
      case 'restartGame':
        this.startNewGame();
        break;
      case 'backToMenu':
        this.switchToMenu();
        break;
    }
  }

  /**
   * 处理游戏结束操作
   */
  handleGameOverAction(action) {
    switch (action) {
      case 'restartGame':
        this.startNewGame();
        break;
      case 'backToMenu':
        this.switchToMenu();
        break;
    }
  }

  /**
   * 切换到菜单
   */
  switchToMenu() {
    this.gameState = 'menu';
    this.cleanupGame();

    // 通知UI切换到菜单
    this.dispatchUIEvent('gameSceneChanged', {
      scene: 'menu',
      data: {
        highScore: this.registry.get('highScore') || 0,
        coins: this.registry.get('coins') || 0,
      },
    });
  }

  /**
   * 开始新游戏
   */
  startNewGame() {
    // 先清理之前的游戏对象
    this.cleanupGame();

    this.gameState = 'playing';
    this.score = 0;
    this.timeWarpEnergy = 100;

    // 创建蛇
    const startX = Math.floor(this.gridWidth / 2) * GAME.GRID_SIZE;
    const startY = Math.floor(this.gridHeight / 2) * GAME.GRID_SIZE;
    this.snake = new Snake(this, startX, startY);

    // 生成食物
    this.generateFood();

    // 通知UI切换到游戏界面
    this.dispatchUIEvent('gameSceneChanged', {
      scene: 'game',
      data: {
        score: this.score,
        energy: this.timeWarpEnergy,
      },
    });

    console.log('新游戏开始');
  }

  /**
   * 暂停游戏
   */
  pauseGame() {
    if (this.gameState === 'playing') {
      this.gameState = 'paused';
      this.physics.pause();

      this.dispatchUIEvent('gameSceneChanged', {
        scene: 'pause',
        data: {},
      });
    }
  }

  /**
   * 恢复游戏
   */
  resumeGame() {
    if (this.gameState === 'paused') {
      this.gameState = 'playing';
      this.physics.resume();

      this.dispatchUIEvent('gameSceneChanged', {
        scene: 'game',
        data: {
          score: this.score,
          energy: this.timeWarpEnergy,
        },
      });
    }
  }

  /**
   * 游戏结束
   */
  gameOver() {
    this.gameState = 'gameOver';

    // 停止蛇的移动但保持显示
    if (this.snake) {
      this.snake.isAlive = false;
    }

    // 计算奖励金币
    const coinsEarned = Math.floor(this.score / 10);
    const currentCoins = this.registry.get('coins') || 0;
    this.registry.set('coins', currentCoins + coinsEarned);

    // 更新最高分
    const currentHighScore = this.registry.get('highScore') || 0;
    if (this.score > currentHighScore) {
      this.registry.set('highScore', this.score);
    }

    this.dispatchUIEvent('gameSceneChanged', {
      scene: 'gameOver',
      data: {
        score: this.score,
        highScore: Math.max(this.score, currentHighScore),
        coinsEarned: coinsEarned,
      },
    });

    console.log('游戏结束，得分:', this.score);
  }

  /**
   * 切换到商店
   */
  switchToShop() {
    this.gameState = 'shop';

    this.dispatchUIEvent('gameSceneChanged', {
      scene: 'shop',
      data: {
        coins: this.registry.get('coins') || 0,
      },
    });
  }

  /**
   * 使用时空倒流
   */
  useTimewarp() {
    if (
      this.gameState === 'playing' &&
      this.snake &&
      this.timeWarpEnergy >= 30
    ) {
      if (this.snake.restoreFromHistory(5)) {
        this.timeWarpEnergy -= 30;
        this.updateGameState();
        console.log('使用时空倒流');
      }
    }
  }

  /**
   * 生成食物
   */
  generateFood() {
    // TODO: 实现食物生成逻辑
    console.log('生成食物');
  }

  /**
   * 处理指针按下
   */
  handlePointerDown(pointer) {
    this.pointerStartX = pointer.x;
    this.pointerStartY = pointer.y;
    this.pointerStartTime = this.time.now;
  }

  /**
   * 处理指针抬起
   */
  handlePointerUp(pointer) {
    if (!this.pointerStartX || !this.pointerStartY) return;

    const deltaX = pointer.x - this.pointerStartX;
    const deltaY = pointer.y - this.pointerStartY;
    const deltaTime = this.time.now - this.pointerStartTime;

    // 检查是否是点击（时空倒流）
    if (Math.abs(deltaX) < 20 && Math.abs(deltaY) < 20 && deltaTime < 200) {
      if (this.gameState === 'playing') {
        this.useTimewarp();
      }
      return;
    }

    // 检查是否是滑动（方向控制）
    if (
      Math.abs(deltaX) > this.swipeThreshold ||
      Math.abs(deltaY) > this.swipeThreshold
    ) {
      let direction;

      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        direction = deltaX > 0 ? DIRECTIONS.RIGHT : DIRECTIONS.LEFT;
      } else {
        direction = deltaY > 0 ? DIRECTIONS.DOWN : DIRECTIONS.UP;
      }

      if (this.snake && this.gameState === 'playing') {
        this.snake.changeDirection(direction);
      }
    }
  }

  /**
   * 更新游戏状态
   */
  update(time, delta) {
    if (this.gameState !== 'playing' || !this.snake) return;

    // 更新蛇
    this.snake.update(time, delta);

    // 检查碰撞
    if (
      this.snake.checkSelfCollision() ||
      this.snake.checkBoundaryCollision()
    ) {
      this.gameOver();
      return;
    }

    // 恢复时空能量
    if (this.timeWarpEnergy < 100) {
      this.timeWarpEnergy += delta * 0.01; // 每秒恢复1点
      this.timeWarpEnergy = Math.min(100, this.timeWarpEnergy);
    }

    // 更新UI状态
    this.updateGameState();
  }

  /**
   * 更新游戏状态到UI
   */
  updateGameState() {
    this.dispatchUIEvent('gameStateChanged', {
      score: this.score,
      energy: this.timeWarpEnergy,
      canUseTimewarp: this.timeWarpEnergy >= 30,
    });
  }

  /**
   * 清理游戏对象
   */
  cleanupGame() {
    if (this.snake) {
      this.snake.destroy();
      this.snake = null;
    }

    if (this.food) {
      // TODO: 清理食物
      this.food = null;
    }
  }

  /**
   * 发送UI事件
   */
  dispatchUIEvent(eventName, data) {
    window.dispatchEvent(new CustomEvent(eventName, { detail: data }));
  }
}
