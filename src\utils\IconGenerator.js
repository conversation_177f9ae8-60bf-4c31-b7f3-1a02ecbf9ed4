/**
 * 图标生成器
 * 用于生成PWA所需的各种尺寸图标
 */
export class IconGenerator {
  /**
   * 生成SVG图标
   */
  static generateSVGIcon() {
    const svg = `
      <svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
          </linearGradient>
          <linearGradient id="snake" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#66BB6A;stop-opacity:1" />
          </linearGradient>
        </defs>
        
        <!-- 背景 -->
        <rect width="512" height="512" rx="64" fill="url(#bg)"/>
        
        <!-- 蛇身 -->
        <rect x="100" y="200" width="40" height="40" rx="8" fill="url(#snake)"/>
        <rect x="140" y="200" width="40" height="40" rx="8" fill="url(#snake)"/>
        <rect x="180" y="200" width="40" height="40" rx="8" fill="url(#snake)"/>
        <rect x="220" y="200" width="40" height="40" rx="8" fill="url(#snake)"/>
        <rect x="260" y="200" width="40" height="40" rx="8" fill="url(#snake)"/>
        <rect x="300" y="200" width="40" height="40" rx="8" fill="url(#snake)"/>
        <rect x="340" y="200" width="40" height="40" rx="8" fill="url(#snake)"/>
        
        <!-- 蛇头 -->
        <rect x="380" y="200" width="40" height="40" rx="8" fill="#388E3C"/>
        
        <!-- 眼睛 -->
        <circle cx="390" cy="210" r="3" fill="white"/>
        <circle cx="410" cy="210" r="3" fill="white"/>
        <circle cx="390" cy="210" r="1.5" fill="black"/>
        <circle cx="410" cy="210" r="1.5" fill="black"/>
        
        <!-- 食物 -->
        <circle cx="200" cy="120" r="15" fill="#FF5722"/>
        <rect x="195" y="105" width="10" height="8" rx="2" fill="#4CAF50"/>
        
        <!-- 时空效果 -->
        <circle cx="256" cy="300" r="60" fill="none" stroke="#8A2BE2" stroke-width="3" opacity="0.6"/>
        <circle cx="256" cy="300" r="40" fill="none" stroke="#8A2BE2" stroke-width="2" opacity="0.4"/>
        <circle cx="256" cy="300" r="20" fill="none" stroke="#8A2BE2" stroke-width="1" opacity="0.2"/>
        
        <!-- 标题 -->
        <text x="256" y="400" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="36" font-weight="bold">时空蛇</text>
        <text x="256" y="430" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="16">Time Warp Snake</text>
      </svg>
    `;
    
    return svg;
  }

  /**
   * 创建Canvas并生成PNG图标
   */
  static generatePNGIcon(size = 512) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      canvas.width = size;
      canvas.height = size;
      const ctx = canvas.getContext('2d');

      // 创建SVG图像
      const svg = this.generateSVGIcon();
      const img = new Image();
      const svgBlob = new Blob([svg], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);

      img.onload = () => {
        ctx.drawImage(img, 0, 0, size, size);
        URL.revokeObjectURL(url);
        
        canvas.toBlob((blob) => {
          resolve(blob);
        }, 'image/png');
      };

      img.src = url;
    });
  }

  /**
   * 生成所有需要的图标尺寸
   */
  static async generateAllIcons() {
    const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
    const icons = {};

    for (const size of sizes) {
      try {
        const blob = await this.generatePNGIcon(size);
        icons[size] = blob;
      } catch (error) {
        console.warn(`生成 ${size}x${size} 图标失败:`, error);
      }
    }

    return icons;
  }

  /**
   * 下载图标文件
   */
  static downloadIcon(blob, filename) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 生成并下载所有图标
   */
  static async generateAndDownloadIcons() {
    console.log('开始生成图标...');
    
    try {
      const icons = await this.generateAllIcons();
      
      Object.entries(icons).forEach(([size, blob]) => {
        this.downloadIcon(blob, `icon-${size}.png`);
      });
      
      // 生成SVG图标
      const svgBlob = new Blob([this.generateSVGIcon()], { type: 'image/svg+xml' });
      this.downloadIcon(svgBlob, 'icon.svg');
      
      console.log('图标生成完成！');
    } catch (error) {
      console.error('图标生成失败:', error);
    }
  }
}
