<!DOCTYPE html>
<html>
<head>
    <title>生成游戏图标</title>
</head>
<body>
    <h1>时空蛇游戏图标生成器</h1>
    <button onclick="generateIcons()">生成所有图标</button>
    <div id="preview"></div>

    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制圆角背景
            const radius = size * 0.125;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();

            // 计算比例
            const scale = size / 512;
            const gridSize = 40 * scale;
            const startX = 100 * scale;
            const startY = 200 * scale;

            // 绘制蛇身
            ctx.fillStyle = '#4CAF50';
            for (let i = 0; i < 7; i++) {
                const x = startX + i * gridSize;
                const y = startY;
                ctx.fillRect(x, y, gridSize - 2 * scale, gridSize - 2 * scale);
            }

            // 绘制蛇头
            ctx.fillStyle = '#388E3C';
            const headX = startX + 7 * gridSize;
            ctx.fillRect(headX, startY, gridSize - 2 * scale, gridSize - 2 * scale);

            // 绘制眼睛
            if (size >= 96) {
                ctx.fillStyle = 'white';
                const eyeSize = 3 * scale;
                ctx.beginPath();
                ctx.arc(headX + 10 * scale, startY + 10 * scale, eyeSize, 0, Math.PI * 2);
                ctx.arc(headX + 30 * scale, startY + 10 * scale, eyeSize, 0, Math.PI * 2);
                ctx.fill();

                ctx.fillStyle = 'black';
                ctx.beginPath();
                ctx.arc(headX + 10 * scale, startY + 10 * scale, eyeSize / 2, 0, Math.PI * 2);
                ctx.arc(headX + 30 * scale, startY + 10 * scale, eyeSize / 2, 0, Math.PI * 2);
                ctx.fill();
            }

            // 绘制食物
            ctx.fillStyle = '#FF5722';
            ctx.beginPath();
            ctx.arc(200 * scale, 120 * scale, 15 * scale, 0, Math.PI * 2);
            ctx.fill();

            // 绘制时空效果
            if (size >= 128) {
                const centerX = 256 * scale;
                const centerY = 300 * scale;
                
                ctx.strokeStyle = 'rgba(138, 43, 226, 0.6)';
                ctx.lineWidth = 3 * scale;
                ctx.beginPath();
                ctx.arc(centerX, centerY, 60 * scale, 0, Math.PI * 2);
                ctx.stroke();
            }

            return canvas;
        }

        function downloadCanvas(canvas, filename) {
            canvas.toBlob((blob) => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/png');
        }

        function generateIcons() {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            const preview = document.getElementById('preview');
            preview.innerHTML = '<p>正在生成图标...</p>';

            // 生成所有图标
            const icons = {};
            sizes.forEach(size => {
                icons[size] = createIcon(size);
            });

            // 清空预览区域
            preview.innerHTML = '';

            // 添加批量下载按钮
            const batchContainer = document.createElement('div');
            batchContainer.style.marginBottom = '20px';

            const batchBtn = document.createElement('button');
            batchBtn.textContent = '下载所有图标';
            batchBtn.style.padding = '10px 20px';
            batchBtn.style.fontSize = '16px';
            batchBtn.style.backgroundColor = '#4CAF50';
            batchBtn.style.color = 'white';
            batchBtn.style.border = 'none';
            batchBtn.style.borderRadius = '4px';
            batchBtn.style.cursor = 'pointer';
            batchBtn.onclick = () => {
                sizes.forEach(size => {
                    setTimeout(() => {
                        downloadCanvas(icons[size], `icon-${size}.png`);
                    }, size * 10); // 延迟下载避免浏览器阻止
                });
            };

            batchContainer.appendChild(batchBtn);
            preview.appendChild(batchContainer);

            // 添加预览
            sizes.forEach(size => {
                const container = document.createElement('div');
                container.style.display = 'inline-block';
                container.style.margin = '10px';
                container.style.textAlign = 'center';
                container.style.border = '1px solid #ddd';
                container.style.padding = '10px';
                container.style.borderRadius = '8px';

                const previewCanvas = createIcon(64); // 预览用小图
                previewCanvas.style.border = '1px solid #ccc';
                previewCanvas.style.borderRadius = '4px';

                const label = document.createElement('div');
                label.textContent = `${size}x${size}`;
                label.style.margin = '8px 0';
                label.style.fontWeight = 'bold';

                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = '下载';
                downloadBtn.style.padding = '4px 8px';
                downloadBtn.style.backgroundColor = '#2196F3';
                downloadBtn.style.color = 'white';
                downloadBtn.style.border = 'none';
                downloadBtn.style.borderRadius = '4px';
                downloadBtn.style.cursor = 'pointer';
                downloadBtn.onclick = () => downloadCanvas(icons[size], `icon-${size}.png`);

                container.appendChild(previewCanvas);
                container.appendChild(label);
                container.appendChild(downloadBtn);
                preview.appendChild(container);
            });

            // 添加说明
            const instructions = document.createElement('div');
            instructions.style.marginTop = '20px';
            instructions.style.padding = '15px';
            instructions.style.backgroundColor = '#f5f5f5';
            instructions.style.borderRadius = '8px';
            instructions.innerHTML = `
                <h3>使用说明：</h3>
                <ol>
                    <li>点击"下载所有图标"或单独下载需要的尺寸</li>
                    <li>将下载的PNG文件放到 <code>public/assets/</code> 目录下</li>
                    <li>确保文件名格式为 <code>icon-{size}.png</code></li>
                    <li>重新启动开发服务器</li>
                </ol>
            `;
            preview.appendChild(instructions);
        }
    </script>
</body>
</html>
