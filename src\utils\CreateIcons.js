/**
 * 创建基础图标的工具函数
 * 在浏览器中运行以生成PNG图标
 */

export function createBasicIcon(size = 192) {
  const canvas = document.createElement('canvas');
  canvas.width = size;
  canvas.height = size;
  const ctx = canvas.getContext('2d');

  // 背景渐变
  const gradient = ctx.createLinearGradient(0, 0, size, size);
  gradient.addColorStop(0, '#667eea');
  gradient.addColorStop(1, '#764ba2');
  
  // 绘制圆角背景
  const radius = size * 0.125; // 12.5% 圆角
  ctx.fillStyle = gradient;
  ctx.beginPath();
  ctx.roundRect(0, 0, size, size, radius);
  ctx.fill();

  // 计算比例
  const scale = size / 512;
  const gridSize = 40 * scale;
  const startX = 100 * scale;
  const startY = 200 * scale;

  // 绘制蛇身
  ctx.fillStyle = '#4CAF50';
  for (let i = 0; i < 7; i++) {
    const x = startX + i * gridSize;
    const y = startY;
    ctx.fillRect(x, y, gridSize - 2 * scale, gridSize - 2 * scale);
  }

  // 绘制蛇头
  ctx.fillStyle = '#388E3C';
  const headX = startX + 7 * gridSize;
  ctx.fillRect(headX, startY, gridSize - 2 * scale, gridSize - 2 * scale);

  // 绘制眼睛
  ctx.fillStyle = 'white';
  const eyeSize = 3 * scale;
  ctx.beginPath();
  ctx.arc(headX + 10 * scale, startY + 10 * scale, eyeSize, 0, Math.PI * 2);
  ctx.arc(headX + 30 * scale, startY + 10 * scale, eyeSize, 0, Math.PI * 2);
  ctx.fill();

  ctx.fillStyle = 'black';
  ctx.beginPath();
  ctx.arc(headX + 10 * scale, startY + 10 * scale, eyeSize / 2, 0, Math.PI * 2);
  ctx.arc(headX + 30 * scale, startY + 10 * scale, eyeSize / 2, 0, Math.PI * 2);
  ctx.fill();

  // 绘制食物
  ctx.fillStyle = '#FF5722';
  ctx.beginPath();
  ctx.arc(200 * scale, 120 * scale, 15 * scale, 0, Math.PI * 2);
  ctx.fill();

  // 绘制时空效果圆圈
  const centerX = 256 * scale;
  const centerY = 300 * scale;
  
  ctx.strokeStyle = 'rgba(138, 43, 226, 0.6)';
  ctx.lineWidth = 3 * scale;
  ctx.beginPath();
  ctx.arc(centerX, centerY, 60 * scale, 0, Math.PI * 2);
  ctx.stroke();

  ctx.strokeStyle = 'rgba(138, 43, 226, 0.4)';
  ctx.lineWidth = 2 * scale;
  ctx.beginPath();
  ctx.arc(centerX, centerY, 40 * scale, 0, Math.PI * 2);
  ctx.stroke();

  // 添加文字（如果尺寸足够大）
  if (size >= 192) {
    ctx.fillStyle = 'white';
    ctx.font = `bold ${36 * scale}px Arial, sans-serif`;
    ctx.textAlign = 'center';
    ctx.fillText('时空蛇', centerX, 400 * scale);
    
    ctx.font = `${16 * scale}px Arial, sans-serif`;
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.fillText('Time Warp Snake', centerX, 430 * scale);
  }

  return canvas;
}

// 生成并下载图标
export function generateAndDownloadIcon(size = 192) {
  const canvas = createBasicIcon(size);
  
  canvas.toBlob((blob) => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `icon-${size}.png`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 'image/png');
}

// 在控制台中运行以生成图标
if (typeof window !== 'undefined') {
  window.generateIcon = generateAndDownloadIcon;
  window.createIcon = createBasicIcon;
}
