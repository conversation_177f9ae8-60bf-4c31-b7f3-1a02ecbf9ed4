import { GameplayScene } from '../scenes/GameplayScene.js';

/**
 * Phaser 3 游戏配置
 * 时空蛇游戏的核心配置文件
 */
export const GameConfig = {
  // 游戏类型
  type: Phaser.AUTO,

  // 游戏尺寸配置
  width: 800,
  height: 600,

  // 父容器
  parent: 'game-container',

  // 背景颜色
  backgroundColor: '#1a1a1a',

  // 缩放配置
  scale: {
    mode: Phaser.Scale.FIT,
    autoCenter: Phaser.Scale.CENTER_BOTH,
    width: 800,
    height: 600,
    min: {
      width: 320,
      height: 240,
    },
    max: {
      width: 1200,
      height: 900,
    },
  },

  // 物理引擎配置
  physics: {
    default: 'arcade',
    arcade: {
      gravity: { y: 0 },
      debug: false,
    },
  },

  // 渲染配置
  render: {
    antialias: true,
    pixelArt: false,
    roundPixels: true,
    transparent: false,
    clearBeforeRender: true,
    preserveDrawingBuffer: false,
    premultipliedAlpha: true,
    failIfMajorPerformanceCaveat: false,
    powerPreference: 'default',
    batchSize: 4096,
    maxLights: 10,
  },

  // 音频配置
  audio: {
    disableWebAudio: false,
    context: false,
    noAudio: false,
  },

  // 输入配置
  input: {
    keyboard: true,
    mouse: true,
    touch: true,
    gamepad: false,
    smoothFactor: 0.2,
    windowEvents: true,
  },

  // DOM配置
  dom: {
    createContainer: true,
  },

  // 场景配置 - 只包含游戏逻辑场景
  scene: [GameplayScene],

  // 回调函数
  callbacks: {
    preBoot: function (game) {
      console.log('游戏预启动');

      // 设置游戏全局变量
      game.registry.set('gameVersion', '1.0.0');
      game.registry.set('gameTitle', '时空蛇');

      // 初始化游戏数据
      game.registry.set('score', 0);
      game.registry.set('highScore', 0);
      game.registry.set('coins', 1000);
      game.registry.set('currentSkin', 'classic');
      game.registry.set('timeWarpEnergy', 100);

      // 设置默认设置
      game.registry.set('settings', {
        musicVolume: 0.3,
        sfxVolume: 0.7,
        vibration: true,
        language: 'zh-CN',
      });
    },

    postBoot: function (game) {
      console.log('游戏启动完成');

      // 设置全局事件监听
      game.events.on('pause', () => {
        console.log('游戏暂停');
      });

      game.events.on('resume', () => {
        console.log('游戏恢复');
      });

      // 移动端特殊处理
      if (game.device.input.touch) {
        console.log('检测到触控设备');
        game.registry.set('isMobile', true);
      } else {
        game.registry.set('isMobile', false);
      }
    },
  },

  // 横幅配置
  banner: {
    hidePhaser: false,
    text: '#4CAF50',
    background: ['#667eea', '#764ba2', '#f093fb', '#f5576c'],
  },

  // FPS配置
  fps: {
    min: 30,
    target: 60,
    forceSetTimeOut: false,
    deltaHistory: 10,
    panicMax: 120,
    smoothStep: true,
  },

  // 加载器配置
  loader: {
    baseURL: '',
    path: '',
    maxParallelDownloads: 32,
    crossOrigin: 'anonymous',
    responseType: '',
    async: true,
    user: '',
    password: '',
    timeout: 0,
    withCredentials: false,
  },

  // 插件配置
  plugins: {
    global: [
      // 这里可以添加全局插件
    ],
    scene: [
      // 这里可以添加场景插件
    ],
  },

  // 实验性功能
  experimental: {
    enableContextMenu: false,
  },
};
