// UI覆盖层
.ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: @z-index-overlay;

  > * {
    pointer-events: auto;
  }
}

// 通知容器
.notification-container {
  position: fixed;
  top: @spacing-md;
  right: @spacing-md;
  z-index: @z-index-modal + 100;
  pointer-events: none;

  .notification {
    display: flex;
    align-items: flex-start;
    background: @bg-light;
    border-radius: @border-radius;
    box-shadow: @shadow-lg;
    margin-bottom: @spacing-sm;
    padding: @spacing-md;
    min-width: 300px;
    max-width: 400px;
    pointer-events: auto;
    transform: translateX(100%);
    opacity: 0;
    animation: slideInNotification @transition-normal ease-out forwards;

    &.notification-removing {
      animation: slideOutNotification @transition-normal ease-in forwards;
    }

    &.notification-error {
      border-left: 4px solid @danger-color;

      .notification-icon {
        color: @danger-color;
      }
    }

    &.notification-success {
      border-left: 4px solid @success-color;

      .notification-icon {
        color: @success-color;
      }
    }

    &.notification-warning {
      border-left: 4px solid @warning-color;

      .notification-icon {
        color: @warning-color;
      }
    }

    &.notification-info {
      border-left: 4px solid @secondary-color;

      .notification-icon {
        color: @secondary-color;
      }
    }

    .notification-icon {
      font-size: @font-size-lg;
      margin-right: @spacing-sm;
      flex-shrink: 0;
    }

    .notification-content {
      flex: 1;

      .notification-title {
        font-weight: bold;
        font-size: @font-size-md;
        color: @text-primary;
        margin-bottom: @spacing-xs;
      }

      .notification-message {
        font-size: @font-size-sm;
        color: @text-secondary;
        line-height: 1.4;
      }
    }

    .notification-close {
      background: none;
      border: none;
      font-size: @font-size-lg;
      color: @text-muted;
      cursor: pointer;
      padding: 0;
      margin-left: @spacing-sm;
      flex-shrink: 0;

      &:hover {
        color: @text-primary;
      }
    }
  }
}

@keyframes slideInNotification {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutNotification {
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

// 分数显示
.score-display {
  position: absolute;
  top: @spacing-md;
  left: @spacing-md;
  background: rgba(0, 0, 0, 0.7);
  color: @text-light;
  padding: @spacing-sm @spacing-md;
  border-radius: @border-radius;
  font-size: @font-size-lg;
  font-weight: bold;
  backdrop-filter: blur(10px);
  
  .score-label {
    font-size: @font-size-sm;
    opacity: 0.8;
    margin-bottom: @spacing-xs;
  }
  
  .score-value {
    font-size: @font-size-xl;
    color: @accent-color;
  }
}

// 时空能量条
.energy-bar {
  position: absolute;
  top: @spacing-md;
  right: @spacing-md;
  width: 120px;
  height: 20px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: @border-radius;
  padding: 2px;
  backdrop-filter: blur(10px);
  
  .energy-fill {
    height: 100%;
    background: linear-gradient(90deg, @snake-timewarp, lighten(@snake-timewarp, 20%));
    border-radius: @border-radius-small;
    transition: width @transition-normal ease;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      border-radius: @border-radius-small;
      animation: shimmer 2s infinite;
    }
  }
  
  .energy-label {
    position: absolute;
    top: -@spacing-lg;
    left: 0;
    font-size: @font-size-xs;
    color: @text-light;
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

// 游戏控制按钮
.game-controls {
  position: absolute;
  bottom: @spacing-md;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: @spacing-md;
  
  .control-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    color: @text-light;
    border: none;
    .center-flex();
    font-size: @font-size-lg;
    cursor: pointer;
    transition: all @transition-normal ease;
    backdrop-filter: blur(10px);
    
    &:hover {
      background: rgba(0, 0, 0, 0.8);
      transform: scale(1.1);
    }
    
    &:active {
      transform: scale(0.95);
    }
    
    &.timewarp-btn {
      background: linear-gradient(135deg, @snake-timewarp, lighten(@snake-timewarp, 20%));
      
      &:disabled {
        background: rgba(0, 0, 0, 0.5);
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
      }
    }
  }
}

// 提示文本
.hint-text {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  color: @text-light;
  font-size: @font-size-sm;
  text-align: center;
  opacity: 0.8;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.4; }
}

// 游戏状态指示器
.game-status {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: @text-light;
  z-index: @z-index-modal;
  
  .status-title {
    font-size: @font-size-xxl;
    font-weight: bold;
    margin-bottom: @spacing-md;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
  
  .status-message {
    font-size: @font-size-lg;
    margin-bottom: @spacing-lg;
    opacity: 0.9;
  }
  
  &.paused {
    .status-title {
      color: @warning-color;
    }
  }
  
  &.game-over {
    .status-title {
      color: @danger-color;
    }
  }
}
