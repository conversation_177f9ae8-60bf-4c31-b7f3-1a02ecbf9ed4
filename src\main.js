import './styles/main.less';
import { GameConfig } from './config/GameConfig.js';
import { UIManager } from './managers/UIManager.js';

/**
 * 时空蛇游戏主入口
 * Time Warp Snake Game Main Entry
 */
class TimeWarpSnakeGame {
  constructor() {
    this.game = null;
    this.uiManager = null;
    this.isInitialized = false;
  }

  /**
   * 初始化游戏
   */
  async init() {
    try {
      // 显示加载界面
      this.showLoading();

      // 检查浏览器兼容性
      if (!this.checkCompatibility()) {
        this.showError('您的浏览器不支持此游戏，请使用现代浏览器');
        return;
      }

      // 注册 Service Worker (PWA)
      await this.registerServiceWorker();

      // 初始化UI管理器
      this.initUIManager();

      // 初始化 Phaser 游戏
      await this.initPhaserGame();

      // 设置移动端适配
      this.setupMobileAdaptation();

      // 隐藏加载界面
      this.hideLoading();

      this.isInitialized = true;
      console.log('时空蛇游戏初始化完成');
    } catch (error) {
      console.error('游戏初始化失败:', error);
      this.showError('游戏加载失败，请刷新页面重试');
    }
  }

  /**
   * 检查浏览器兼容性
   */
  checkCompatibility() {
    // 检查必要的 Web API
    const requiredAPIs = [
      'localStorage',
      'requestAnimationFrame',
      'addEventListener',
      'Canvas2DContext' in window ? 'getContext' : null,
    ].filter(Boolean);

    return requiredAPIs.every(api => {
      if (api === 'getContext') {
        const canvas = document.createElement('canvas');
        return !!(canvas.getContext && canvas.getContext('2d'));
      }
      return api in window;
    });
  }

  /**
   * 注册 Service Worker
   */
  async registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker 注册成功:', registration);
      } catch (error) {
        console.warn('Service Worker 注册失败:', error);
      }
    }
  }

  /**
   * 初始化UI管理器
   */
  initUIManager() {
    this.uiManager = new UIManager();
    console.log('UI管理器初始化完成');
  }

  /**
   * 初始化 Phaser 游戏
   */
  async initPhaserGame() {
    // 动态导入 Phaser
    const Phaser = await import('phaser');

    // 创建游戏实例
    this.game = new Phaser.Game(GameConfig);

    // 将UI管理器实例传递给游戏
    this.game.uiManager = this.uiManager;

    // 设置游戏事件监听
    this.setupGameEvents();
  }

  /**
   * 设置游戏事件监听
   */
  setupGameEvents() {
    if (!this.game) return;

    // 监听游戏准备就绪事件
    this.game.events.on('ready', () => {
      console.log('Phaser 游戏准备就绪');
    });

    // 监听游戏暂停/恢复事件
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.game.scene.pause();
      } else {
        this.game.scene.resume();
      }
    });
  }

  /**
   * 设置移动端适配
   */
  setupMobileAdaptation() {
    // 防止页面缩放
    document.addEventListener(
      'touchstart',
      e => {
        if (e.touches.length > 1) {
          e.preventDefault();
        }
      },
      { passive: false }
    );

    // 防止双击缩放
    let lastTouchEnd = 0;
    document.addEventListener(
      'touchend',
      e => {
        const now = new Date().getTime();
        if (now - lastTouchEnd <= 300) {
          e.preventDefault();
        }
        lastTouchEnd = now;
      },
      false
    );

    // 监听屏幕方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        if (this.game && this.game.scale) {
          this.game.scale.refresh();
        }
      }, 100);
    });

    // 设置视口高度 CSS 变量（解决移动端 100vh 问题）
    const setViewportHeight = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    setViewportHeight();
    window.addEventListener('resize', setViewportHeight);
  }

  /**
   * 显示加载界面
   */
  showLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
      loading.style.display = 'block';
    }
  }

  /**
   * 隐藏加载界面
   */
  hideLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
      loading.style.display = 'none';
    }
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    // 如果游戏已初始化，使用通知系统
    if (this.isInitialized && this.game && this.game.scene.scenes.length > 0) {
      const activeScene = this.game.scene.scenes.find(scene =>
        scene.scene.isActive()
      );
      if (activeScene && activeScene.notificationManager) {
        activeScene.notificationManager.showError(message, 0); // 0 = 不自动消失
        return;
      }
    }

    // 否则使用传统的DOM方式
    const loading = document.getElementById('loading');
    if (loading) {
      loading.innerHTML = `
        <div style="color: #f44336; text-align: center;">
          <div style="font-size: 24px; margin-bottom: 16px;">⚠️</div>
          <div>${message}</div>
          <button onclick="location.reload()" style="
            margin-top: 16px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          ">重新加载</button>
        </div>
      `;
    }
  }

  /**
   * 销毁游戏
   */
  destroy() {
    if (this.uiManager) {
      this.uiManager.destroy();
      this.uiManager = null;
    }

    if (this.game) {
      this.game.destroy(true);
      this.game = null;
    }

    this.isInitialized = false;
  }
}

// 创建游戏实例并初始化
const timeWarpSnake = new TimeWarpSnakeGame();

// 页面加载完成后初始化游戏
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => timeWarpSnake.init());
} else {
  timeWarpSnake.init();
}

// 导出游戏实例供调试使用
window.timeWarpSnake = timeWarpSnake;
