# Design Document

## Overview

时空蛇是一个基于 Phaser 3 引擎的创新移动端贪吃蛇游戏。核心创新在于时空倒流机制，允许玩家消耗能量回到之前的位置避免碰撞。游戏采用模块化架构，支持 PWA 安装，具有完整的商业化功能。

### 核心特性

- 时空倒流机制：策略性的容错系统
- 元素系统：火、水、电等视觉效果
- 移动端优化：触控操作和响应式设计
- 商业化功能：皮肤、道具、货币系统
- PWA 支持：可安装、离线运行

## Architecture

### 技术栈

- **游戏引擎**: Phaser 3.70+
- **构建工具**: Vite
- **语言**: ES6+ JavaScript
- **样式**: Less CSS
- **代码质量**: ESLint + Prettier
- **PWA**: Service Worker + Web App Manifest
- **粒子效果**: phaser3-rex-plugins (粒子插件) 或 three-particles

### 项目结构

```
src/
├── main.js                 # 应用入口点
├── config/                 # 游戏配置
│   ├── GameConfig.js       # Phaser游戏配置
│   └── Constants.js        # 游戏常量
├── scenes/                 # 游戏场景
│   ├── BootScene.js        # 启动场景
│   ├── MenuScene.js        # 主菜单场景
│   ├── GameScene.js        # 主游戏场景
│   ├── PauseScene.js       # 暂停场景
│   ├── GameOverScene.js    # 游戏结束场景
│   └── ShopScene.js        # 商店场景
├── entities/               # 游戏实体
│   ├── Snake.js            # 蛇类
│   ├── Food.js             # 食物类
│   └── ParticleManager.js  # 粒子效果管理器
├── systems/                # 游戏系统
│   ├── InputSystem.js      # 输入处理系统
│   ├── CollisionSystem.js  # 碰撞检测系统
│   ├── TimeWarpSystem.js   # 时空倒流系统
│   └── ElementSystem.js    # 元素效果系统
├── managers/               # 管理器
│   ├── GameStateManager.js # 游戏状态管理
│   ├── SoundManager.js     # 音效管理
│   ├── SaveManager.js      # 存档管理
│   └── ShopManager.js      # 商店管理
├── utils/                  # 工具类
│   ├── MobileUtils.js      # 移动端工具
│   ├── MathUtils.js        # 数学工具
│   └── CanvasUtils.js      # Canvas绘制工具
├── styles/                 # 样式文件
│   ├── main.less           # 主样式文件
│   ├── components/         # 组件样式
│   │   ├── ui.less         # UI组件样式
│   │   ├── buttons.less    # 按钮样式
│   │   └── modals.less     # 弹窗样式
│   ├── scenes/             # 场景样式
│   │   ├── menu.less       # 菜单场景样式
│   │   ├── game.less       # 游戏场景样式
│   │   └── shop.less       # 商店场景样式
│   └── utils/              # 样式工具
│       ├── variables.less  # Less变量
│       ├── mixins.less     # Less混入
│       └── responsive.less # 响应式样式
└── assets/                 # 资源文件
    ├── audio/              # 音频文件
    ├── fonts/              # 字体文件
    └── manifest.json       # PWA清单
```

### 架构模式

- **场景管理**: Phaser Scene 系统管理不同游戏状态
- **实体组件**: 游戏对象采用组件化设计
- **事件驱动**: 使用 Phaser 事件系统解耦模块
- **状态管理**: 集中式游戏状态管理
- **资源管理**: 延迟加载和资源池化

## Components and Interfaces

### 核心游戏实体

#### Snake 类

```javascript
class Snake {
  constructor(scene, x, y) {
    this.scene = scene;
    this.body = []; // 蛇身节点数组
    this.direction = "RIGHT"; // 当前移动方向
    this.nextDirection = "RIGHT"; // 下一个方向
    this.speed = 150; // 移动速度(ms)
    this.skin = "classic"; // 当前皮肤
    this.element = "none"; // 当前元素效果
    this.history = []; // 位置历史记录
  }

  // 核心方法
  update(time, delta) {} // 更新蛇的状态
  move() {} // 移动蛇
  grow() {} // 增长蛇身
  changeDirection(direction) {} // 改变方向
  checkCollision() {} // 碰撞检测
  applyElement(element) {} // 应用元素效果
  render() {} // 渲染蛇
}
```

#### Food 类

```javascript
class Food {
  constructor(scene, x, y, type) {
    this.scene = scene;
    this.x = x;
    this.y = y;
    this.type = type; // 食物类型
    this.value = 10; // 分数值
    this.effect = null; // 特殊效果
    this.sprite = null; // 显示对象
  }

  // 核心方法
  create() {} // 创建食物
  destroy() {} // 销毁食物
  getRandomPosition() {} // 获取随机位置
  applyEffect(snake) {} // 应用食物效果
}
```

### 系统组件

#### InputSystem 类

```javascript
class InputSystem {
  constructor(scene) {
    this.scene = scene;
    this.swipeThreshold = 50; // 滑动阈值
    this.tapThreshold = 200; // 点击时间阈值
    this.startTouch = null; // 触摸开始位置
    this.startTime = 0; // 触摸开始时间
  }

  // 核心方法
  init() {} // 初始化输入监听
  handleTouchStart(event) {} // 处理触摸开始
  handleTouchEnd(event) {} // 处理触摸结束
  detectSwipe() {} // 检测滑动方向
  detectTap() {} // 检测点击
}
```

#### TimeWarpSystem 类

```javascript
class TimeWarpSystem {
  constructor(snake) {
    this.snake = snake;
    this.energy = 100; // 时空能量
    this.maxEnergy = 100; // 最大能量
    this.regenRate = 1; // 能量恢复速度
    this.cost = 30; // 使用消耗
    this.historyLength = 10; // 历史记录长度
  }

  // 核心方法
  update(delta) {} // 更新能量
  canUse() {} // 检查是否可用
  activate() {} // 激活时空倒流
  saveHistory() {} // 保存历史状态
  restoreHistory() {} // 恢复历史状态
}
```

### 管理器组件

#### GameStateManager 类

```javascript
class GameStateManager {
  constructor() {
    this.score = 0; // 当前分数
    this.highScore = 0; // 最高分
    this.coins = 0; // 游戏币
    this.level = 1; // 当前等级
    this.gameState = "MENU"; // 游戏状态
    this.settings = {}; // 游戏设置
  }

  // 核心方法
  updateScore(points) {} // 更新分数
  addCoins(amount) {} // 增加金币
  saveGame() {} // 保存游戏
  loadGame() {} // 加载游戏
  resetGame() {} // 重置游戏
}
```

#### SoundManager 类

```javascript
class SoundManager {
  constructor(scene) {
    this.scene = scene;
    this.sounds = {}; // 音效对象
    this.musicVolume = 0.3; // 音乐音量
    this.sfxVolume = 0.7; // 音效音量
    this.enabled = true; // 是否启用音效
  }

  // 核心方法
  preload() {} // 预加载音效
  playSound(key, config) {} // 播放音效
  playMusic(key, loop) {} // 播放音乐
  stopAll() {} // 停止所有音效
  setVolume(type, volume) {} // 设置音量
}
```

## Data Models

### 游戏状态数据结构

#### GameState

```javascript
const GameState = {
  player: {
    score: 0,
    highScore: 0,
    coins: 1000,
    level: 1,
    totalGames: 0,
    totalTime: 0,
  },
  snake: {
    skin: "classic",
    element: "none",
    speed: 150,
    length: 3,
  },
  shop: {
    skins: {
      classic: { unlocked: true, price: 0 },
      fire: { unlocked: false, price: 500 },
      water: { unlocked: false, price: 500 },
      electric: { unlocked: false, price: 750 },
      rainbow: { unlocked: false, price: 1000 },
      galaxy: { unlocked: false, price: 1500 },
    },
    items: {
      doubleScore: { count: 0, price: 100 },
      timeBoost: { count: 0, price: 150 },
      magnet: { count: 0, price: 200 },
      shield: { count: 0, price: 250 },
    },
  },
  settings: {
    musicVolume: 0.3,
    sfxVolume: 0.7,
    vibration: true,
    language: "zh-CN",
  },
};
```

#### Food Types

```javascript
const FoodTypes = {
  NORMAL: {
    emoji: "🍎",
    color: "#FF0000",
    score: 10,
    effect: null,
  },
  FIRE: {
    emoji: "🍓",
    color: "#FF4500",
    score: 20,
    effect: "fire",
  },
  WATER: {
    emoji: "🍇",
    color: "#0080FF",
    score: 20,
    effect: "water",
  },
  ELECTRIC: {
    emoji: "🍊",
    color: "#FFD700",
    score: 20,
    effect: "electric",
  },
  TIMEWARP: {
    emoji: "🍑",
    color: "#8A2BE2",
    score: 30,
    effect: "timewarp",
  },
  GOLDEN: {
    emoji: "🍍",
    color: "#FFD700",
    score: 50,
    effect: "golden",
  },
};
```

### 皮肤系统数据结构

#### Skin Configuration

```javascript
const SkinConfig = {
  classic: {
    name: "经典绿色",
    bodyColor: "#4CAF50",
    headColor: "#388E3C",
    eyeColor: "#000000",
    price: 0,
    unlocked: true,
  },
  fire: {
    name: "火焰蛇",
    bodyColor: "#FF5722",
    headColor: "#D84315",
    eyeColor: "#FFEB3B",
    glowColor: "#FF9800",
    price: 500,
    unlocked: false,
  },
  water: {
    name: "水流蛇",
    bodyColor: "#2196F3",
    headColor: "#1976D2",
    eyeColor: "#E3F2FD",
    glowColor: "#00BCD4",
    price: 500,
    unlocked: false,
  },
  // ... 其他皮肤配置
};
```

## Error Handling

### 错误类型和处理策略

#### 游戏运行时错误

```javascript
class GameErrorHandler {
  static handleError(error, context) {
    console.error(`Game Error in ${context}:`, error);

    switch (error.type) {
      case "COLLISION_ERROR":
        // 碰撞检测错误 - 重置游戏状态
        this.resetGameState();
        break;

      case "SAVE_ERROR":
        // 存档错误 - 使用本地缓存
        this.fallbackToLocalStorage();
        break;

      case "AUDIO_ERROR":
        // 音频错误 - 禁用音效继续游戏
        this.disableAudio();
        break;

      case "RENDER_ERROR":
        // 渲染错误 - 降级到简单模式
        this.enableFallbackMode();
        break;

      default:
        // 未知错误 - 显示错误信息
        this.showErrorMessage(error.message);
    }
  }
}
```

#### 网络和存储错误

```javascript
class StorageErrorHandler {
  static async saveWithFallback(key, data) {
    try {
      // 尝试使用IndexedDB
      await this.saveToIndexedDB(key, data);
    } catch (error) {
      try {
        // 降级到localStorage
        localStorage.setItem(key, JSON.stringify(data));
      } catch (fallbackError) {
        // 最后降级到内存存储
        this.memoryStorage[key] = data;
        console.warn("Using memory storage as fallback");
      }
    }
  }
}
```

#### 移动端特定错误

```javascript
class MobileErrorHandler {
  static handleTouchError(error) {
    // 触控事件错误处理
    if (error.type === "TOUCH_NOT_SUPPORTED") {
      // 降级到键盘控制
      this.enableKeyboardControls();
    }
  }

  static handleOrientationError(error) {
    // 屏幕方向错误处理
    if (error.type === "ORIENTATION_LOCK_FAILED") {
      // 显示方向提示
      this.showOrientationHint();
    }
  }
}
```

## Testing Strategy

### 测试层级

#### 单元测试

- **实体类测试**: Snake, Food, Particle 类的核心逻辑
- **系统测试**: InputSystem, TimeWarpSystem, CollisionSystem
- **管理器测试**: GameStateManager, SoundManager, SaveManager
- **工具类测试**: MobileUtils, MathUtils, CanvasUtils

#### 集成测试

- **场景切换测试**: 各场景间的状态传递
- **游戏流程测试**: 完整游戏循环的集成测试
- **存储系统测试**: 数据持久化和恢复
- **商店系统测试**: 购买流程和状态更新

#### 移动端测试

- **触控测试**: 各种触控手势的识别准确性
- **性能测试**: 不同设备上的帧率和内存使用
- **兼容性测试**: 不同浏览器和设备的兼容性
- **PWA 测试**: 安装、离线运行、更新机制

#### 用户体验测试

- **响应性测试**: 不同屏幕尺寸的适配
- **可访问性测试**: 色盲友好、字体大小等
- **游戏平衡性测试**: 难度曲线和奖励机制
- **商业化测试**: 购买流程和价格平衡

### 测试工具和框架

- **单元测试**: Jest + jsdom
- **E2E 测试**: Playwright
- **性能测试**: Lighthouse + WebPageTest
- **移动端测试**: BrowserStack + 真机测试

### 测试数据和场景

```javascript
const TestScenarios = {
  gameplay: {
    normalGame: "正常游戏流程测试",
    timeWarp: "时空倒流功能测试",
    collision: "碰撞检测测试",
    foodConsumption: "食物消费测试",
  },
  mobile: {
    swipeDetection: "滑动检测测试",
    touchResponse: "触控响应测试",
    orientationChange: "屏幕旋转测试",
    performanceOnLowEnd: "低端设备性能测试",
  },
  business: {
    skinPurchase: "皮肤购买测试",
    itemUsage: "道具使用测试",
    coinEarning: "金币获取测试",
    saveRestore: "存档恢复测试",
  },
};
```

### 性能指标

- **帧率**: 目标 60FPS，最低 30FPS
- **内存使用**: 移动端<100MB，桌面端<200MB
- **加载时间**: 首次加载<3 秒，后续<1 秒
- **电池消耗**: 每小时<10%电量消耗
- **网络使用**: 离线模式下 0 网络消耗

## 粒子效果系统设计

### 推荐插件选择

#### 方案一：phaser3-rex-plugins

- **优势**: 专为 Phaser 3 设计，集成度高
- **功能**: 丰富的粒子效果、UI 组件、特效
- **安装**: `npm install phaser3-rex-plugins`
- **适用场景**: 复杂的粒子效果和 UI 需求

#### 方案二：particles.js

- **优势**: 轻量级，性能优秀
- **功能**: 专注粒子效果，配置简单
- **安装**: `npm install particles.js`
- **适用场景**: 简单的背景粒子和基础特效

### 粒子效果配置

#### 元素效果配置

```javascript
const ParticleConfigs = {
  fire: {
    texture: "particle",
    scale: { start: 0.5, end: 0 },
    speed: { min: 50, max: 100 },
    lifespan: 600,
    tint: [0xff6600, 0xff0000, 0xffaa00],
    blendMode: "ADD",
  },
  water: {
    texture: "particle",
    scale: { start: 0.3, end: 0 },
    speed: { min: 30, max: 80 },
    lifespan: 800,
    tint: [0x0066ff, 0x00aaff, 0x66ccff],
    alpha: { start: 0.8, end: 0 },
  },
  electric: {
    texture: "particle",
    scale: { start: 0.4, end: 0 },
    speed: { min: 80, max: 150 },
    lifespan: 400,
    tint: [0xffff00, 0xffffff, 0x66ff66],
    blendMode: "ADD",
  },
  timewarp: {
    texture: "particle",
    scale: { start: 0.6, end: 0 },
    speed: { min: 20, max: 60 },
    lifespan: 1000,
    tint: [0x8a2be2, 0x9932cc, 0xba55d3],
    alpha: { start: 1, end: 0 },
  },
};
```

#### ParticleManager 实现

```javascript
class ParticleManager {
  constructor(scene) {
    this.scene = scene;
    this.emitters = new Map();
    this.trailEffects = new Map();
  }

  init() {
    // 创建粒子纹理
    this.createParticleTexture();

    // 初始化粒子发射器
    this.initEmitters();
  }

  createParticleTexture() {
    // 创建简单的圆形粒子纹理
    const graphics = this.scene.add.graphics();
    graphics.fillStyle(0xffffff);
    graphics.fillCircle(4, 4, 4);
    graphics.generateTexture("particle", 8, 8);
    graphics.destroy();
  }

  createElementEffect(x, y, element) {
    const config = ParticleConfigs[element];
    if (!config) return null;

    const emitter = this.scene.add.particles(x, y, "particle", {
      ...config,
      quantity: 5,
      frequency: 100,
    });

    // 自动销毁
    this.scene.time.delayedCall(1000, () => {
      emitter.destroy();
    });

    return emitter;
  }

  createSnakeTrail(snake) {
    if (snake.element === "none") return;

    const trailConfig = {
      ...ParticleConfigs[snake.element],
      follow: snake.head,
      quantity: 2,
      frequency: 50,
    };

    const trail = this.scene.add.particles(0, 0, "particle", trailConfig);
    this.trailEffects.set(snake.id, trail);

    return trail;
  }

  destroyTrail(snakeId) {
    const trail = this.trailEffects.get(snakeId);
    if (trail) {
      trail.destroy();
      this.trailEffects.delete(snakeId);
    }
  }

  createTimeWarpEffect(centerX, centerY) {
    // 时空扭曲的螺旋效果
    const emitter = this.scene.add.particles(centerX, centerY, "particle", {
      ...ParticleConfigs.timewarp,
      quantity: 20,
      frequency: 50,
      emitZone: {
        type: "edge",
        source: new Phaser.Geom.Circle(0, 0, 100),
        quantity: 20,
      },
      moveToX: centerX,
      moveToY: centerY,
    });

    // 添加螺旋运动
    emitter.onParticleEmit((particle) => {
      const angle = Phaser.Math.Angle.Between(
        centerX,
        centerY,
        particle.x,
        particle.y
      );
      particle.velocityX = Math.cos(angle + Math.PI / 2) * 50;
      particle.velocityY = Math.sin(angle + Math.PI / 2) * 50;
    });

    this.scene.time.delayedCall(1500, () => {
      emitter.destroy();
    });

    return emitter;
  }
}
```

### 性能优化策略

#### 粒子池化

```javascript
class ParticlePool {
  constructor(scene, maxSize = 100) {
    this.scene = scene;
    this.pool = [];
    this.maxSize = maxSize;
  }

  getParticle() {
    if (this.pool.length > 0) {
      return this.pool.pop();
    }
    return this.createNewParticle();
  }

  releaseParticle(particle) {
    if (this.pool.length < this.maxSize) {
      particle.reset();
      this.pool.push(particle);
    } else {
      particle.destroy();
    }
  }
}
```

#### 动态质量调整

```javascript
class PerformanceManager {
  constructor() {
    this.particleQuality = "high"; // high, medium, low
    this.frameRate = 60;
    this.frameHistory = [];
  }

  update() {
    this.trackFrameRate();
    this.adjustQuality();
  }

  adjustQuality() {
    if (this.frameRate < 30) {
      this.particleQuality = "low";
    } else if (this.frameRate < 45) {
      this.particleQuality = "medium";
    } else {
      this.particleQuality = "high";
    }
  }

  getParticleConfig(baseConfig) {
    const qualityMultipliers = {
      low: 0.3,
      medium: 0.6,
      high: 1.0,
    };

    const multiplier = qualityMultipliers[this.particleQuality];
    return {
      ...baseConfig,
      quantity: Math.ceil(baseConfig.quantity * multiplier),
      frequency: baseConfig.frequency / multiplier,
    };
  }
}
```
