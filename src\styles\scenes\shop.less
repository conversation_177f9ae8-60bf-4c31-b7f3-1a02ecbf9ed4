// 商店场景样式
.shop-scene {
  background: @bg-primary;
  min-height: 100vh;
  padding: @spacing-lg;
  
  .shop-container {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .shop-header {
    text-align: center;
    margin-bottom: @spacing-xl;
    
    .shop-title {
      font-size: 2.5rem;
      font-weight: bold;
      color: @text-light;
      margin-bottom: @spacing-sm;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }
    
    .coins-display {
      background: linear-gradient(135deg, @warning-color, darken(@warning-color, 10%));
      color: @text-primary;
      padding: @spacing-md @spacing-lg;
      border-radius: @border-radius-large;
      display: inline-flex;
      align-items: center;
      gap: @spacing-sm;
      font-size: @font-size-lg;
      font-weight: bold;
      box-shadow: @shadow-md;
      
      .coin-icon {
        font-size: 1.5em;
      }
    }
  }
  
  .shop-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: @spacing-lg;
    background: rgba(0, 0, 0, 0.3);
    border-radius: @border-radius;
    padding: @spacing-xs;
    backdrop-filter: blur(10px);
    
    .shop-tab {
      flex: 1;
      max-width: 150px;
      padding: @spacing-sm @spacing-md;
      background: transparent;
      color: rgba(255, 255, 255, 0.7);
      border: none;
      border-radius: @border-radius-small;
      cursor: pointer;
      transition: all @transition-normal ease;
      font-size: @font-size-md;
      
      &:hover {
        color: @text-light;
        background: rgba(255, 255, 255, 0.1);
      }
      
      &.active {
        background: @primary-color;
        color: @text-light;
      }
    }
  }
  
  .shop-content {
    .shop-section {
      display: none;
      
      &.active {
        display: block;
      }
    }
    
    .section-title {
      font-size: @font-size-xl;
      color: @text-light;
      margin-bottom: @spacing-lg;
      text-align: center;
    }
    
    .shop-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: @spacing-lg;
      
      .mobile-only({
        grid-template-columns: repeat(2, 1fr);
        gap: @spacing-md;
      });
    }
  }
  
  .shop-item {
    .card();
    text-align: center;
    padding: @spacing-lg;
    border: 2px solid transparent;
    transition: all @transition-normal ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    
    &:hover:not(.purchased):not(.locked) {
      border-color: @primary-color;
      transform: translateY(-4px);
      box-shadow: @shadow-lg;
    }
    
    &.purchased {
      background: linear-gradient(135deg, @success-color, darken(@success-color, 10%));
      color: @text-light;
      border-color: @success-color;
      
      &::after {
        content: '✓';
        position: absolute;
        top: @spacing-sm;
        right: @spacing-sm;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 24px;
        height: 24px;
        .center-flex();
        font-weight: bold;
      }
    }
    
    &.locked {
      opacity: 0.6;
      cursor: not-allowed;
      
      &::before {
        content: '🔒';
        position: absolute;
        top: @spacing-sm;
        right: @spacing-sm;
        font-size: @font-size-lg;
      }
    }
    
    .item-preview {
      width: 80px;
      height: 80px;
      margin: 0 auto @spacing-md;
      border-radius: @border-radius;
      .center-flex();
      font-size: 2rem;
      position: relative;
      
      &.skin-preview {
        border: 3px solid;
        
        &.classic { border-color: @snake-classic; }
        &.fire { border-color: @snake-fire; }
        &.water { border-color: @snake-water; }
        &.electric { border-color: @snake-electric; }
        &.timewarp { border-color: @snake-timewarp; }
      }
      
      &.item-preview {
        background: linear-gradient(135deg, @accent-color, darken(@accent-color, 10%));
        color: @text-light;
      }
    }
    
    .item-name {
      font-size: @font-size-lg;
      font-weight: bold;
      margin-bottom: @spacing-sm;
      color: @text-primary;
    }
    
    .item-description {
      font-size: @font-size-sm;
      color: @text-secondary;
      margin-bottom: @spacing-md;
      line-height: 1.4;
    }
    
    .item-price {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: @spacing-xs;
      font-size: @font-size-lg;
      font-weight: bold;
      color: @warning-color;
      
      .coin-icon {
        font-size: 1.2em;
      }
    }
    
    .purchase-btn {
      .btn;
      .btn-primary();
      width: 100%;
      margin-top: @spacing-md;
      
      &:disabled {
        background: @text-muted;
        cursor: not-allowed;
      }
    }
  }
  
  .shop-footer {
    text-align: center;
    margin-top: @spacing-xl;
    
    .back-btn {
      .btn;
      .btn-secondary();
      padding: @spacing-md @spacing-xl;
    }
  }
}

// 皮肤预览动画
.skin-preview {
  &.fire {
    animation: fireGlow 2s ease-in-out infinite alternate;
  }
  
  &.water {
    animation: waterFlow 3s ease-in-out infinite;
  }
  
  &.electric {
    animation: electricSpark 1.5s ease-in-out infinite;
  }
  
  &.timewarp {
    animation: timewarpPulse 2s ease-in-out infinite alternate;
  }
}

@keyframes fireGlow {
  0% { box-shadow: 0 0 10px @snake-fire; }
  100% { box-shadow: 0 0 20px @snake-fire, 0 0 30px @snake-fire; }
}

@keyframes waterFlow {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes electricSpark {
  0%, 100% { 
    box-shadow: 0 0 5px @snake-electric;
    transform: rotate(0deg);
  }
  25% { 
    box-shadow: 0 0 15px @snake-electric;
    transform: rotate(1deg);
  }
  75% { 
    box-shadow: 0 0 15px @snake-electric;
    transform: rotate(-1deg);
  }
}

// 购买成功动画
.purchase-success {
  animation: purchaseSuccess 0.6s ease-out;
}

@keyframes purchaseSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
