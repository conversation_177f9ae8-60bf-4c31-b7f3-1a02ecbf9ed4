import {
  DIRECTIONS,
  DIRECTION_VECTORS,
  GAME,
  SKIN_CONFIG,
} from '../config/Constants.js';

/**
 * 蛇类
 * 管理蛇的状态、移动、渲染等核心功能
 */
export class Snake {
  constructor(scene, x, y) {
    this.scene = scene;
    this.gridSize = GAME.GRID_SIZE;

    // 蛇身数据结构 - 每个节点包含网格坐标
    this.body = [];
    this.initializeBody(x, y);

    // 移动相关
    this.direction = DIRECTIONS.RIGHT;
    this.nextDirection = DIRECTIONS.RIGHT;
    this.speed = GAME.INITIAL_SPEED;
    this.lastMoveTime = 0;

    // 外观相关
    this.skin = 'classic';
    this.element = 'none';
    this.elementDuration = 0;

    // 历史记录（用于时空倒流）
    this.history = [];
    this.maxHistoryLength = 10;

    // 渲染对象
    this.graphics = scene.add.graphics();
    this.headGraphics = scene.add.graphics();

    // 状态标志
    this.isAlive = true;
    this.isGrowing = false;
  }

  /**
   * 初始化蛇身
   */
  initializeBody(startX, startY) {
    const gridX = Math.floor(startX / this.gridSize);
    const gridY = Math.floor(startY / this.gridSize);

    // 创建初始蛇身（从头到尾）
    for (let i = 0; i < GAME.INITIAL_SNAKE_LENGTH; i++) {
      this.body.push({
        x: gridX - i,
        y: gridY,
      });
    }
  }

  /**
   * 更新蛇的状态
   */
  update(time, delta) {
    if (!this.isAlive) return;

    // 更新元素效果持续时间
    this.updateElementEffect(delta);

    // 检查是否到了移动时间
    if (time - this.lastMoveTime >= this.speed) {
      this.move();
      this.lastMoveTime = time;
    }

    // 渲染蛇
    this.render();
  }

  /**
   * 移动蛇
   */
  move() {
    if (!this.isAlive) return;

    // 保存当前状态到历史记录
    this.saveToHistory();

    // 更新方向
    this.direction = this.nextDirection;

    // 计算新的头部位置
    const head = this.body[0];
    const dirVector = DIRECTION_VECTORS[this.direction];
    const newHead = {
      x: head.x + dirVector.x,
      y: head.y + dirVector.y,
    };

    // 添加新头部
    this.body.unshift(newHead);

    // 如果不是在增长，移除尾部
    if (!this.isGrowing) {
      this.body.pop();
    } else {
      this.isGrowing = false;
    }
  }

  /**
   * 改变移动方向
   */
  changeDirection(newDirection) {
    // 防止反向移动
    const opposites = {
      [DIRECTIONS.UP]: DIRECTIONS.DOWN,
      [DIRECTIONS.DOWN]: DIRECTIONS.UP,
      [DIRECTIONS.LEFT]: DIRECTIONS.RIGHT,
      [DIRECTIONS.RIGHT]: DIRECTIONS.LEFT,
    };

    if (opposites[this.direction] !== newDirection) {
      this.nextDirection = newDirection;
    }
  }

  /**
   * 增长蛇身
   */
  grow() {
    this.isGrowing = true;
  }

  /**
   * 获取头部位置
   */
  getHead() {
    return this.body[0];
  }

  /**
   * 获取头部的像素坐标
   */
  getHeadPixelPosition() {
    const head = this.getHead();
    return {
      x: head.x * this.gridSize + this.gridSize / 2,
      y: head.y * this.gridSize + this.gridSize / 2,
    };
  }

  /**
   * 检查是否与自身碰撞
   */
  checkSelfCollision() {
    const head = this.body[0];

    // 检查头部是否与身体其他部分重叠
    for (let i = 1; i < this.body.length; i++) {
      const segment = this.body[i];
      if (head.x === segment.x && head.y === segment.y) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查是否与边界碰撞
   */
  checkBoundaryCollision() {
    const head = this.body[0];
    const gameWidth = this.scene.cameras.main.width;
    const gameHeight = this.scene.cameras.main.height;
    const maxGridX = Math.floor(gameWidth / this.gridSize) - 1;
    const maxGridY = Math.floor(gameHeight / this.gridSize) - 1;

    return head.x < 0 || head.x > maxGridX || head.y < 0 || head.y > maxGridY;
  }

  /**
   * 应用元素效果
   */
  applyElement(elementType, duration = 5000) {
    this.element = elementType;
    this.elementDuration = duration;
  }

  /**
   * 更新元素效果
   */
  updateElementEffect(delta) {
    if (this.element !== 'none' && this.elementDuration > 0) {
      this.elementDuration -= delta;
      if (this.elementDuration <= 0) {
        this.element = 'none';
        this.elementDuration = 0;
      }
    }
  }

  /**
   * 保存状态到历史记录
   */
  saveToHistory() {
    const state = {
      body: this.body.map(segment => ({ ...segment })),
      direction: this.direction,
      element: this.element,
      elementDuration: this.elementDuration,
    };

    this.history.unshift(state);

    // 限制历史记录长度
    if (this.history.length > this.maxHistoryLength) {
      this.history.pop();
    }
  }

  /**
   * 从历史记录恢复状态
   */
  restoreFromHistory(steps = 1) {
    if (this.history.length === 0) return false;

    const targetIndex = Math.min(steps - 1, this.history.length - 1);
    const state = this.history[targetIndex];

    if (state) {
      this.body = state.body.map(segment => ({ ...segment }));
      this.direction = state.direction;
      this.nextDirection = state.direction;
      this.element = state.element;
      this.elementDuration = state.elementDuration;

      // 清除使用的历史记录
      this.history.splice(0, targetIndex + 1);

      return true;
    }

    return false;
  }

  /**
   * 渲染蛇
   */
  render() {
    this.graphics.clear();
    this.headGraphics.clear();

    const skinConfig =
      SKIN_CONFIG[this.skin.toUpperCase()] || SKIN_CONFIG.CLASSIC;

    // 渲染蛇身
    this.renderBody(skinConfig);

    // 渲染蛇头
    this.renderHead(skinConfig);
  }

  /**
   * 渲染蛇身
   */
  renderBody(skinConfig) {
    this.graphics.fillStyle(skinConfig.bodyColor);

    // 跳过头部，只渲染身体
    for (let i = 1; i < this.body.length; i++) {
      const segment = this.body[i];
      const x = segment.x * this.gridSize;
      const y = segment.y * this.gridSize;

      // 绘制方块身体
      this.graphics.fillRect(
        x + 1,
        y + 1,
        this.gridSize - 2,
        this.gridSize - 2
      );

      // 添加元素效果发光
      if (this.element !== 'none' && skinConfig.glowColor) {
        this.graphics.lineStyle(2, skinConfig.glowColor, 0.6);
        this.graphics.strokeRect(x, y, this.gridSize, this.gridSize);
      }
    }
  }

  /**
   * 渲染蛇头
   */
  renderHead(skinConfig) {
    if (this.body.length === 0) return;

    const head = this.body[0];
    const x = head.x * this.gridSize;
    const y = head.y * this.gridSize;

    // 绘制头部方块
    this.headGraphics.fillStyle(skinConfig.headColor);
    this.headGraphics.fillRect(
      x + 1,
      y + 1,
      this.gridSize - 2,
      this.gridSize - 2
    );

    // 绘制眼睛
    this.renderEyes(x, y, skinConfig.eyeColor);

    // 添加元素效果发光
    if (this.element !== 'none' && skinConfig.glowColor) {
      this.headGraphics.lineStyle(3, skinConfig.glowColor, 0.8);
      this.headGraphics.strokeRect(x, y, this.gridSize, this.gridSize);
    }
  }

  /**
   * 渲染眼睛
   */
  renderEyes(x, y, eyeColor) {
    const eyeSize = 3;
    const eyeOffset = 5;

    this.headGraphics.fillStyle(eyeColor);

    // 根据方向调整眼睛位置
    switch (this.direction) {
      case DIRECTIONS.UP:
        this.headGraphics.fillCircle(x + eyeOffset, y + eyeOffset, eyeSize);
        this.headGraphics.fillCircle(
          x + this.gridSize - eyeOffset,
          y + eyeOffset,
          eyeSize
        );
        break;
      case DIRECTIONS.DOWN:
        this.headGraphics.fillCircle(
          x + eyeOffset,
          y + this.gridSize - eyeOffset,
          eyeSize
        );
        this.headGraphics.fillCircle(
          x + this.gridSize - eyeOffset,
          y + this.gridSize - eyeOffset,
          eyeSize
        );
        break;
      case DIRECTIONS.LEFT:
        this.headGraphics.fillCircle(x + eyeOffset, y + eyeOffset, eyeSize);
        this.headGraphics.fillCircle(
          x + eyeOffset,
          y + this.gridSize - eyeOffset,
          eyeSize
        );
        break;
      case DIRECTIONS.RIGHT:
        this.headGraphics.fillCircle(
          x + this.gridSize - eyeOffset,
          y + eyeOffset,
          eyeSize
        );
        this.headGraphics.fillCircle(
          x + this.gridSize - eyeOffset,
          y + this.gridSize - eyeOffset,
          eyeSize
        );
        break;
    }
  }

  /**
   * 设置皮肤
   */
  setSkin(skinId) {
    if (SKIN_CONFIG[skinId.toUpperCase()]) {
      this.skin = skinId;
    }
  }

  /**
   * 设置速度
   */
  setSpeed(newSpeed) {
    this.speed = Math.max(GAME.MIN_SPEED, Math.min(GAME.MAX_SPEED, newSpeed));
  }

  /**
   * 销毁蛇
   */
  destroy() {
    this.isAlive = false;

    if (this.graphics) {
      this.graphics.clear();
      this.graphics.destroy();
      this.graphics = null;
    }

    if (this.headGraphics) {
      this.headGraphics.clear();
      this.headGraphics.destroy();
      this.headGraphics = null;
    }

    // 清理其他引用
    this.body = [];
    this.history = [];
    this.scene = null;
  }
}
