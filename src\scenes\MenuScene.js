import Phaser from 'phaser';
import { NotificationManager } from '../managers/NotificationManager.js';

/**
 * 主菜单场景
 * 游戏的主菜单界面
 */
export class MenuScene extends Phaser.Scene {
  constructor() {
    super({ key: 'MenuScene' });
  }

  create() {
    console.log('MenuScene: 主菜单场景启动');

    // 初始化管理器
    this.notificationManager = new NotificationManager(this);

    // 获取全局UI管理器实例
    this.uiManager = this.game.uiManager;

    // 显示菜单UI
    this.uiManager.showMenuUI({});

    // 监听UI事件
    this.uiManager.on('startGame', () => {
      this.scene.start('GameplayScene');
    });

    this.uiManager.on('showShop', () => {
      this.scene.start('ShopScene');
    });

    this.uiManager.on('showSettings', () => {
      // TODO: 实现设置界面
      this.notificationManager.showNotification('设置功能即将推出！', 'info');
    });
  }

  destroy() {
    if (this.uiManager) {
      this.uiManager.destroy();
    }
  }
}
