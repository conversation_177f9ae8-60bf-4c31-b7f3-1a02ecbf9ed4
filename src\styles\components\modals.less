// 模态框背景
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  z-index: @z-index-modal;
  .center-flex();
  
  &.fade-in {
    animation: fadeIn @transition-normal ease-out;
  }
  
  &.fade-out {
    animation: fadeOut @transition-normal ease-out;
  }
}

// 模态框容器
.modal {
  background: @bg-light;
  border-radius: @border-radius-large;
  box-shadow: @shadow-lg;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  
  &.slide-up {
    animation: slideUpModal @transition-normal ease-out;
  }
  
  &.slide-down {
    animation: slideDownModal @transition-normal ease-out;
  }
  
  .mobile-only({
    max-width: 95vw;
    margin: @spacing-md;
  });
}

// 模态框头部
.modal-header {
  padding: @spacing-lg;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .modal-title {
    font-size: @font-size-xl;
    font-weight: bold;
    color: @text-primary;
    margin: 0;
  }
  
  .modal-close {
    background: none;
    border: none;
    font-size: @font-size-lg;
    cursor: pointer;
    color: @text-secondary;
    padding: @spacing-xs;
    border-radius: @border-radius-small;
    transition: all @transition-fast ease;
    
    &:hover {
      background: rgba(0, 0, 0, 0.1);
      color: @text-primary;
    }
  }
}

// 模态框内容
.modal-body {
  padding: @spacing-lg;
  max-height: 60vh;
  overflow-y: auto;
  .hide-scrollbar();
  
  .modal-text {
    font-size: @font-size-md;
    line-height: 1.6;
    color: @text-secondary;
    margin-bottom: @spacing-md;
  }
}

// 模态框底部
.modal-footer {
  padding: @spacing-lg;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  gap: @spacing-md;
  justify-content: flex-end;
  
  .mobile-only({
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  });
}

// 特定模态框样式
.game-over-modal {
  .modal;
  text-align: center;
  min-width: 300px;
  
  .modal-header {
    background: linear-gradient(135deg, @danger-color, darken(@danger-color, 10%));
    color: @text-light;
    border-bottom: none;
    
    .modal-title {
      color: @text-light;
    }
    
    .modal-close {
      color: @text-light;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
  
  .final-score {
    font-size: @font-size-xxl;
    font-weight: bold;
    color: @accent-color;
    margin: @spacing-md 0;
  }
  
  .high-score {
    font-size: @font-size-lg;
    color: @success-color;
    margin-bottom: @spacing-lg;
  }
  
  .coins-earned {
    background: linear-gradient(135deg, @warning-color, darken(@warning-color, 10%));
    color: @text-primary;
    padding: @spacing-sm @spacing-md;
    border-radius: @border-radius;
    display: inline-block;
    margin-bottom: @spacing-lg;
    font-weight: bold;
  }
}

.pause-modal {
  .modal;
  text-align: center;
  min-width: 250px;
  
  .modal-header {
    background: linear-gradient(135deg, @warning-color, darken(@warning-color, 10%));
    color: @text-primary;
    border-bottom: none;
    
    .modal-title {
      color: @text-primary;
    }
  }
}

.shop-modal {
  .modal;
  min-width: 400px;
  max-width: 600px;
  
  .mobile-only({
    min-width: auto;
    width: 100%;
  });
  
  .modal-header {
    background: linear-gradient(135deg, @primary-color, darken(@primary-color, 10%));
    color: @text-light;
    border-bottom: none;
    
    .modal-title {
      color: @text-light;
    }
    
    .modal-close {
      color: @text-light;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
  
  .shop-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: @spacing-md;
    
    .mobile-only({
      grid-template-columns: repeat(2, 1fr);
    });
  }
}

// 动画
@keyframes slideUpModal {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDownModal {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
