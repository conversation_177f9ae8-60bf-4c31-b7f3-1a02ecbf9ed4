// 响应式断点
@mobile-max: 767px;
@tablet-min: 768px;
@tablet-max: 1023px;
@desktop-min: 1024px;
@large-desktop-min: 1440px;

// 移动端样式
@media (max-width: @mobile-max) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
  }
  
  .mobile-center {
    text-align: center !important;
  }
  
  // 游戏容器移动端适配
  #game-container {
    padding: @spacing-sm;
  }
  
  // 按钮移动端适配
  .btn {
    min-height: 44px; // iOS推荐的最小触控区域
    padding: @spacing-md @spacing-lg;
  }
  
  // 文字大小调整
  .text-responsive {
    font-size: @font-size-sm;
  }
}

// 平板样式
@media (min-width: @tablet-min) and (max-width: @tablet-max) {
  .tablet-hidden {
    display: none !important;
  }
  
  .tablet-full-width {
    width: 100% !important;
  }
  
  // 游戏容器平板适配
  #game-container {
    padding: @spacing-md;
  }
}

// 桌面端样式
@media (min-width: @desktop-min) {
  .desktop-hidden {
    display: none !important;
  }
  
  .desktop-full-width {
    width: 100% !important;
  }
  
  // 游戏容器桌面端适配
  #game-container {
    padding: @spacing-lg;
    max-width: 800px;
    margin: 0 auto;
  }
}

// 大屏幕样式
@media (min-width: @large-desktop-min) {
  #game-container {
    max-width: 1000px;
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 600px) {
  .landscape-hidden {
    display: none !important;
  }
  
  #game-container {
    height: 100vh;
    padding: @spacing-xs;
  }
  
  // 横屏时调整UI布局
  .ui-overlay {
    flex-direction: row;
    
    .ui-left {
      flex: 1;
    }
    
    .ui-right {
      flex: 1;
    }
  }
}

// 竖屏适配
@media (orientation: portrait) {
  .portrait-hidden {
    display: none !important;
  }
  
  // 竖屏时的UI布局
  .ui-overlay {
    flex-direction: column;
  }
}

// 高DPI屏幕适配
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  // 高清屏幕下的样式调整
  .high-dpi {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
