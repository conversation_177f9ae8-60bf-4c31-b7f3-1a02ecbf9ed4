// 按钮混入
.button-base() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: @spacing-sm @spacing-md;
  border: none;
  border-radius: @border-radius;
  font-size: @font-size-md;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all @transition-normal ease;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: @shadow-md;
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

.button-primary() {
  .button-base();
  background: @primary-color;
  color: @text-light;
  
  &:hover:not(:disabled) {
    background: darken(@primary-color, 10%);
  }
}

.button-secondary() {
  .button-base();
  background: @secondary-color;
  color: @text-light;
  
  &:hover:not(:disabled) {
    background: darken(@secondary-color, 10%);
  }
}

// 卡片混入
.card() {
  background: @bg-light;
  border-radius: @border-radius;
  box-shadow: @shadow-sm;
  padding: @spacing-md;
  transition: box-shadow @transition-normal ease;
  
  &:hover {
    box-shadow: @shadow-md;
  }
}

// 居中混入
.center-flex() {
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-absolute() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 响应式混入
.mobile-only(@rules) {
  @media (max-width: 768px) {
    @rules();
  }
}

.tablet-up(@rules) {
  @media (min-width: 769px) {
    @rules();
  }
}

.desktop-up(@rules) {
  @media (min-width: 1024px) {
    @rules();
  }
}

// 文字省略
.text-ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 清除浮动
.clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 隐藏滚动条
.hide-scrollbar() {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}
