/**
 * 通知管理器
 * 统一管理游戏中的各种UI反馈：错误提示、成就解锁、Toast消息等
 */
export class NotificationManager {
  constructor(scene) {
    this.scene = scene;
    this.notifications = [];
    this.maxNotifications = 5;
    this.container = null;
    this.init();
  }

  /**
   * 初始化通知系统
   */
  init() {
    // 创建通知容器
    this.container = this.scene.add.container(0, 0);
    this.container.setDepth(10000); // 确保在最顶层
  }

  /**
   * 显示错误消息
   */
  showError(message, duration = 4000) {
    return this.showNotification({
      type: 'error',
      title: '错误',
      message: message,
      duration: duration,
      icon: '⚠️',
      color: '#F44336',
      backgroundColor: '#FFEBEE'
    });
  }

  /**
   * 显示成功消息
   */
  showSuccess(message, duration = 3000) {
    return this.showNotification({
      type: 'success',
      title: '成功',
      message: message,
      duration: duration,
      icon: '✅',
      color: '#4CAF50',
      backgroundColor: '#E8F5E8'
    });
  }

  /**
   * 显示警告消息
   */
  showWarning(message, duration = 3000) {
    return this.showNotification({
      type: 'warning',
      title: '警告',
      message: message,
      duration: duration,
      icon: '⚠️',
      color: '#FF9800',
      backgroundColor: '#FFF3E0'
    });
  }

  /**
   * 显示信息消息
   */
  showInfo(message, duration = 3000) {
    return this.showNotification({
      type: 'info',
      title: '提示',
      message: message,
      duration: duration,
      icon: 'ℹ️',
      color: '#2196F3',
      backgroundColor: '#E3F2FD'
    });
  }

  /**
   * 显示成就解锁
   */
  showAchievement(achievementName, description, duration = 5000) {
    return this.showNotification({
      type: 'achievement',
      title: '成就解锁',
      message: `${achievementName}\n${description}`,
      duration: duration,
      icon: '🏆',
      color: '#FFD700',
      backgroundColor: '#FFF8E1',
      special: true
    });
  }

  /**
   * 显示金币获得
   */
  showCoinsEarned(amount, duration = 2000) {
    return this.showNotification({
      type: 'coins',
      title: '金币获得',
      message: `+${amount} 金币`,
      duration: duration,
      icon: '🪙',
      color: '#FFD700',
      backgroundColor: '#FFF8E1',
      compact: true
    });
  }

  /**
   * 显示分数增加
   */
  showScoreIncrease(points, duration = 1500) {
    return this.showNotification({
      type: 'score',
      title: '',
      message: `+${points}`,
      duration: duration,
      icon: '',
      color: '#4CAF50',
      backgroundColor: 'transparent',
      compact: true,
      floating: true
    });
  }

  /**
   * 显示通用通知
   */
  showNotification(config) {
    // 如果通知太多，移除最旧的
    if (this.notifications.length >= this.maxNotifications) {
      this.removeNotification(this.notifications[0]);
    }

    const notification = this.createNotification(config);
    this.notifications.push(notification);
    
    // 重新排列通知位置
    this.arrangeNotifications();

    // 自动移除
    if (config.duration > 0) {
      this.scene.time.delayedCall(config.duration, () => {
        this.removeNotification(notification);
      });
    }

    return notification;
  }

  /**
   * 创建通知UI
   */
  createNotification(config) {
    const width = this.scene.cameras.main.width;
    const notificationWidth = config.compact ? 200 : 300;
    const notificationHeight = config.compact ? 50 : 80;
    
    // 创建通知容器
    const notificationContainer = this.scene.add.container(width - notificationWidth - 20, 0);
    
    // 背景
    const background = this.scene.add.graphics();
    background.fillStyle(Phaser.Display.Color.HexStringToColor(config.backgroundColor).color);
    background.fillRoundedRect(0, 0, notificationWidth, notificationHeight, 8);
    
    // 边框
    background.lineStyle(2, Phaser.Display.Color.HexStringToColor(config.color).color);
    background.strokeRoundedRect(0, 0, notificationWidth, notificationHeight, 8);
    
    notificationContainer.add(background);

    // 图标
    if (config.icon) {
      const icon = this.scene.add.text(15, notificationHeight / 2, config.icon, {
        fontSize: config.compact ? '16px' : '20px'
      }).setOrigin(0, 0.5);
      notificationContainer.add(icon);
    }

    // 标题和消息
    const textX = config.icon ? 45 : 15;
    
    if (config.title && !config.compact) {
      const title = this.scene.add.text(textX, 20, config.title, {
        fontSize: '14px',
        fontWeight: 'bold',
        color: config.color,
        wordWrap: { width: notificationWidth - textX - 15 }
      });
      notificationContainer.add(title);
    }

    const messageY = (config.title && !config.compact) ? 40 : notificationHeight / 2;
    const message = this.scene.add.text(textX, messageY, config.message, {
      fontSize: config.compact ? '14px' : '12px',
      color: config.color,
      wordWrap: { width: notificationWidth - textX - 15 }
    }).setOrigin(0, config.compact ? 0.5 : 0);
    notificationContainer.add(message);

    // 关闭按钮（非紧凑模式）
    if (!config.compact && !config.floating) {
      const closeButton = this.scene.add.text(notificationWidth - 20, 15, '×', {
        fontSize: '16px',
        color: config.color
      }).setOrigin(0.5).setInteractive();
      
      closeButton.on('pointerdown', () => {
        this.removeNotification({ container: notificationContainer, config });
      });
      
      notificationContainer.add(closeButton);
    }

    // 添加到主容器
    this.container.add(notificationContainer);

    // 入场动画
    notificationContainer.setAlpha(0);
    notificationContainer.x += 50;
    
    this.scene.tweens.add({
      targets: notificationContainer,
      alpha: 1,
      x: width - notificationWidth - 20,
      duration: 300,
      ease: 'Back.easeOut'
    });

    // 特殊效果（成就等）
    if (config.special) {
      this.addSpecialEffects(notificationContainer, config);
    }

    return {
      container: notificationContainer,
      config: config
    };
  }

  /**
   * 添加特殊效果
   */
  addSpecialEffects(container, config) {
    // 发光效果
    this.scene.tweens.add({
      targets: container,
      scaleX: 1.05,
      scaleY: 1.05,
      duration: 500,
      yoyo: true,
      repeat: 2,
      ease: 'Sine.easeInOut'
    });

    // 粒子效果（如果有粒子系统）
    // TODO: 添加粒子效果
  }

  /**
   * 移除通知
   */
  removeNotification(notification) {
    if (!notification || !notification.container) return;

    const index = this.notifications.indexOf(notification);
    if (index > -1) {
      this.notifications.splice(index, 1);
    }

    // 退场动画
    this.scene.tweens.add({
      targets: notification.container,
      alpha: 0,
      x: notification.container.x + 50,
      duration: 200,
      ease: 'Back.easeIn',
      onComplete: () => {
        notification.container.destroy();
        this.arrangeNotifications();
      }
    });
  }

  /**
   * 重新排列通知位置
   */
  arrangeNotifications() {
    let currentY = 20;
    
    this.notifications.forEach((notification, index) => {
      if (notification.container && notification.container.active) {
        const targetY = currentY;
        
        this.scene.tweens.add({
          targets: notification.container,
          y: targetY,
          duration: 200,
          ease: 'Power2'
        });
        
        const height = notification.config.compact ? 50 : 80;
        currentY += height + 10;
      }
    });
  }

  /**
   * 清除所有通知
   */
  clearAll() {
    this.notifications.forEach(notification => {
      if (notification.container) {
        notification.container.destroy();
      }
    });
    this.notifications = [];
  }

  /**
   * 销毁通知管理器
   */
  destroy() {
    this.clearAll();
    if (this.container) {
      this.container.destroy();
    }
  }
}
