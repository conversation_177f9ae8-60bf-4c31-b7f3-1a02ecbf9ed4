// 导入工具样式
@import 'utils/variables.less';
@import 'utils/mixins.less';
@import 'utils/responsive.less';

// 导入组件样式
@import 'components/ui.less';
@import 'components/buttons.less';
@import 'components/modals.less';

// 导入场景样式
@import 'scenes/menu.less';
@import 'scenes/game.less';
@import 'scenes/shop.less';

// 全局重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: @font-size-md;
  line-height: 1.5;
  color: @text-primary;
  background: @bg-primary;
  overflow: hidden;
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

// 游戏容器
#game-container {
  width: 100vw;
  height: 100vh;
  .center-flex();
  position: relative;
}

// 加载界面
#loading {
  .center-absolute();
  color: @text-light;
  font-size: @font-size-lg;
  text-align: center;
  z-index: @z-index-overlay;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid @text-light;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto @spacing-md;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

// 游戏画布
canvas {
  display: block;
  border-radius: @border-radius;
  box-shadow: @shadow-lg;
  max-width: 100%;
  max-height: 100%;
}

// 通用工具类
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden !important;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.no-scroll {
  overflow: hidden;
}

// 动画类
.fade-in {
  animation: fadeIn @transition-normal ease-in-out;
}

.fade-out {
  animation: fadeOut @transition-normal ease-in-out;
}

.slide-up {
  animation: slideUp @transition-normal ease-out;
}

.slide-down {
  animation: slideDown @transition-normal ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes slideDown {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}
