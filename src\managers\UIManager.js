/**
 * UI管理器
 * 管理所有HTML/CSS UI元素，与Phaser游戏逻辑分离
 */
export class UIManager {
  constructor() {
    this.currentScene = 'loading';
    this.gameContainer = null;
    this.uiContainer = null;
    this.notificationManager = null;
    this.init();
  }

  /**
   * 初始化UI管理器
   */
  init() {
    this.gameContainer = document.getElementById('game-container');
    this.createUIContainer();
    this.setupEventListeners();
  }

  /**
   * 创建UI容器
   */
  createUIContainer() {
    // 创建UI覆盖层
    this.uiContainer = document.createElement('div');
    this.uiContainer.id = 'ui-overlay';
    this.uiContainer.className = 'ui-overlay';
    this.gameContainer.appendChild(this.uiContainer);

    // 初始化通知管理器（HTML版本）
    this.notificationManager = new HTMLNotificationManager(this.uiContainer);
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 监听游戏事件
    window.addEventListener('gameSceneChanged', event => {
      this.switchScene(event.detail.scene, event.detail.data);
    });

    window.addEventListener('gameStateChanged', event => {
      this.updateGameState(event.detail);
    });
  }

  /**
   * 切换场景UI
   */
  switchScene(sceneName, data = {}) {
    this.currentScene = sceneName;
    this.clearUI();

    switch (sceneName) {
      case 'menu':
        this.showMenuUI(data);
        break;
      case 'game':
        this.showGameUI(data);
        break;
      case 'pause':
        this.showPauseUI(data);
        break;
      case 'gameOver':
        this.showGameOverUI(data);
        break;
      case 'shop':
        this.showShopUI(data);
        break;
      default:
        break;
    }
  }

  /**
   * 清除当前UI
   */
  clearUI() {
    this.uiContainer.innerHTML = '';
  }

  /**
   * 显示菜单UI
   */
  showMenuUI(data) {
    const menuHTML = `
      <div class="menu-scene">
        <div class="menu-container">
          <h1 class="game-title">时空蛇</h1>
          <p class="game-subtitle">Time Warp Snake</p>

          <div class="menu-buttons">
            <button class="menu-btn" data-action="startGame">开始游戏</button>
            <button class="menu-btn" data-action="showShop">商店</button>
            <button class="menu-btn" data-action="showSettings">设置</button>
          </div>
        </div>
      </div>
    `;

    this.uiContainer.innerHTML = menuHTML;
    this.bindMenuEvents();
  }

  /**
   * 显示游戏UI
   */
  showGameUI(data) {
    const gameHTML = `
      <div class="game-ui">
        <div class="top-bar">
          <div class="score-panel">
            <div class="score-label">分数</div>
            <div class="score-value" id="score-display">${data.score || 0}</div>
          </div>
          
          <div class="energy-panel">
            <div class="energy-label">时空能量</div>
            <div class="energy-bar-container">
              <div class="energy-bar-fill" id="energy-bar" style="width: ${data.energy || 100}%"></div>
            </div>
            <div class="energy-text" id="energy-text">${data.energy || 100}/100</div>
          </div>
        </div>
        
        <div class="bottom-controls">
          <button class="control-button pause-btn" data-action="pauseGame">⏸️</button>
          <button class="control-button timewarp-btn" id="timewarp-btn" data-action="useTimewarp">⏰</button>
        </div>
        
        <div class="game-hints mobile-hints">
          <div class="hint-text">滑动控制方向，点击使用时空倒流</div>
        </div>
      </div>
    `;

    this.uiContainer.innerHTML = gameHTML;
    this.bindGameEvents();
  }

  /**
   * 显示暂停UI
   */
  showPauseUI(data) {
    const pauseHTML = `
      <div class="modal-backdrop">
        <div class="pause-modal">
          <div class="modal-header">
            <h2 class="modal-title">游戏暂停</h2>
          </div>
          <div class="modal-body">
            <div class="btn-group btn-group-vertical">
              <button class="btn btn-primary" data-action="resumeGame">继续游戏</button>
              <button class="btn btn-secondary" data-action="restartGame">重新开始</button>
              <button class="btn btn-secondary" data-action="backToMenu">返回菜单</button>
            </div>
          </div>
        </div>
      </div>
    `;

    this.uiContainer.innerHTML = pauseHTML;
    this.bindPauseEvents();
  }

  /**
   * 显示游戏结束UI
   */
  showGameOverUI(data) {
    const isNewRecord = data.score > data.highScore;

    const gameOverHTML = `
      <div class="modal-backdrop">
        <div class="game-over-modal">
          <div class="modal-header">
            <h2 class="modal-title">游戏结束</h2>
          </div>
          <div class="modal-body">
            <div class="final-score">${data.score || 0}</div>
            ${isNewRecord ? '<div class="high-score">新纪录！</div>' : `<div class="high-score">最高分: ${data.highScore || 0}</div>`}
            <div class="coins-earned">获得金币: +${data.coinsEarned || 0}</div>
            
            <div class="btn-group btn-group-vertical">
              <button class="btn btn-primary" data-action="restartGame">重新开始</button>
              <button class="btn btn-secondary" data-action="backToMenu">返回菜单</button>
            </div>
          </div>
        </div>
      </div>
    `;

    this.uiContainer.innerHTML = gameOverHTML;
    this.bindGameOverEvents();
  }

  /**
   * 显示商店UI
   */
  showShopUI(data) {
    const shopHTML = `
      <div class="shop-scene">
        <div class="shop-container">
          <div class="shop-header">
            <h1 class="shop-title">商店</h1>
            <div class="coins-display">
              <span class="coin-icon">🪙</span>
              <span id="shop-coins">${data.coins || 0}</span>
            </div>
          </div>
          
          <div class="shop-tabs">
            <button class="shop-tab active" data-tab="skins">皮肤</button>
            <button class="shop-tab" data-tab="items">道具</button>
          </div>
          
          <div class="shop-content">
            <div class="shop-section active" id="skins-section">
              <div class="shop-grid" id="skins-grid">
                <!-- 皮肤项目将通过JS动态生成 -->
              </div>
            </div>
            
            <div class="shop-section" id="items-section">
              <div class="shop-grid" id="items-grid">
                <!-- 道具项目将通过JS动态生成 -->
              </div>
            </div>
          </div>
          
          <div class="shop-footer">
            <button class="btn btn-secondary back-btn" data-action="backToMenu">返回菜单</button>
          </div>
        </div>
      </div>
    `;

    this.uiContainer.innerHTML = shopHTML;
    this.bindShopEvents();
    this.populateShopItems(data);
  }

  /**
   * 绑定菜单事件
   */
  bindMenuEvents() {
    this.uiContainer.addEventListener('click', event => {
      const action = event.target.dataset.action;
      if (action) {
        this.dispatchGameEvent('menuAction', { action });
      }
    });
  }

  /**
   * 绑定游戏事件
   */
  bindGameEvents() {
    this.uiContainer.addEventListener('click', event => {
      const action = event.target.dataset.action;
      if (action) {
        this.dispatchGameEvent('gameAction', { action });
      }
    });
  }

  /**
   * 绑定暂停事件
   */
  bindPauseEvents() {
    this.uiContainer.addEventListener('click', event => {
      const action = event.target.dataset.action;
      if (action) {
        this.dispatchGameEvent('pauseAction', { action });
      }
    });
  }

  /**
   * 绑定游戏结束事件
   */
  bindGameOverEvents() {
    this.uiContainer.addEventListener('click', event => {
      const action = event.target.dataset.action;
      if (action) {
        this.dispatchGameEvent('gameOverAction', { action });
      }
    });
  }

  /**
   * 绑定商店事件
   */
  bindShopEvents() {
    this.uiContainer.addEventListener('click', event => {
      const action = event.target.dataset.action;
      const tab = event.target.dataset.tab;

      if (action) {
        this.dispatchGameEvent('shopAction', { action, target: event.target });
      }

      if (tab) {
        this.switchShopTab(tab);
      }
    });
  }

  /**
   * 切换商店标签
   */
  switchShopTab(tabName) {
    // 更新标签状态
    this.uiContainer.querySelectorAll('.shop-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    this.uiContainer
      .querySelector(`[data-tab="${tabName}"]`)
      .classList.add('active');

    // 更新内容区域
    this.uiContainer.querySelectorAll('.shop-section').forEach(section => {
      section.classList.remove('active');
    });
    this.uiContainer
      .getElementById(`${tabName}-section`)
      .classList.add('active');
  }

  /**
   * 填充商店项目
   */
  populateShopItems(data) {
    // TODO: 根据数据动态生成商店项目
    console.log('填充商店项目:', data);
  }

  /**
   * 更新游戏状态
   */
  updateGameState(state) {
    if (this.currentScene === 'game') {
      // 更新分数
      const scoreDisplay = document.getElementById('score-display');
      if (scoreDisplay && state.score !== undefined) {
        scoreDisplay.textContent = state.score;
      }

      // 更新能量条
      const energyBar = document.getElementById('energy-bar');
      const energyText = document.getElementById('energy-text');
      if (energyBar && state.energy !== undefined) {
        energyBar.style.width = `${state.energy}%`;
        if (energyText) {
          energyText.textContent = `${Math.round(state.energy)}/100`;
        }
      }

      // 更新时空倒流按钮状态
      const timewarpBtn = document.getElementById('timewarp-btn');
      if (timewarpBtn && state.canUseTimewarp !== undefined) {
        timewarpBtn.disabled = !state.canUseTimewarp;
      }
    }
  }

  /**
   * 发送游戏事件
   */
  dispatchGameEvent(eventName, data) {
    window.dispatchEvent(new CustomEvent(eventName, { detail: data }));
  }

  /**
   * 显示通知
   */
  showNotification(type, message, duration) {
    if (this.notificationManager) {
      this.notificationManager[
        `show${type.charAt(0).toUpperCase() + type.slice(1)}`
      ](message, duration);
    }
  }

  /**
   * 销毁UI管理器
   */
  destroy() {
    if (this.notificationManager) {
      this.notificationManager.destroy();
    }
    if (this.uiContainer) {
      this.uiContainer.remove();
    }
  }
}

/**
 * HTML通知管理器
 * 基于DOM的通知系统
 */
class HTMLNotificationManager {
  constructor(container) {
    this.container = container;
    this.notifications = [];
    this.maxNotifications = 5;
    this.init();
  }

  init() {
    this.notificationContainer = document.createElement('div');
    this.notificationContainer.className = 'notification-container';
    this.container.appendChild(this.notificationContainer);
  }

  showError(message, duration = 4000) {
    return this.createNotification('error', '错误', message, duration);
  }

  showSuccess(message, duration = 3000) {
    return this.createNotification('success', '成功', message, duration);
  }

  showWarning(message, duration = 3000) {
    return this.createNotification('warning', '警告', message, duration);
  }

  showInfo(message, duration = 3000) {
    return this.createNotification('info', '提示', message, duration);
  }

  createNotification(type, title, message, duration) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    notification.innerHTML = `
      <div class="notification-icon">${this.getIcon(type)}</div>
      <div class="notification-content">
        <div class="notification-title">${title}</div>
        <div class="notification-message">${message}</div>
      </div>
      <button class="notification-close">×</button>
    `;

    // 添加关闭事件
    notification
      .querySelector('.notification-close')
      .addEventListener('click', () => {
        this.removeNotification(notification);
      });

    // 添加到容器
    this.notificationContainer.appendChild(notification);
    this.notifications.push(notification);

    // 限制通知数量
    if (this.notifications.length > this.maxNotifications) {
      this.removeNotification(this.notifications[0]);
    }

    // 自动移除
    if (duration > 0) {
      setTimeout(() => {
        this.removeNotification(notification);
      }, duration);
    }

    return notification;
  }

  removeNotification(notification) {
    const index = this.notifications.indexOf(notification);
    if (index > -1) {
      this.notifications.splice(index, 1);
    }

    notification.classList.add('notification-removing');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }

  getIcon(type) {
    const icons = {
      error: '⚠️',
      success: '✅',
      warning: '⚠️',
      info: 'ℹ️',
    };
    return icons[type] || 'ℹ️';
  }

  destroy() {
    if (this.notificationContainer) {
      this.notificationContainer.remove();
    }
  }
}
