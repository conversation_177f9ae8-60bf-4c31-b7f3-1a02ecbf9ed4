// 颜色变量
@primary-color: #4CAF50;
@secondary-color: #2196F3;
@accent-color: #FF9800;
@danger-color: #F44336;
@warning-color: #FFC107;
@success-color: #4CAF50;

// 游戏主题颜色
@snake-classic: #4CAF50;
@snake-fire: #FF5722;
@snake-water: #2196F3;
@snake-electric: #FFD700;
@snake-timewarp: #8A2BE2;

// 背景颜色
@bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
@bg-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
@bg-dark: #1a1a1a;
@bg-light: #ffffff;

// 文字颜色
@text-primary: #333333;
@text-secondary: #666666;
@text-light: #ffffff;
@text-muted: #999999;

// 尺寸变量
@grid-size: 20px;
@border-radius: 8px;
@border-radius-small: 4px;
@border-radius-large: 12px;

// 间距变量
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 16px;
@spacing-lg: 24px;
@spacing-xl: 32px;

// 字体大小
@font-size-xs: 12px;
@font-size-sm: 14px;
@font-size-md: 16px;
@font-size-lg: 18px;
@font-size-xl: 24px;
@font-size-xxl: 32px;

// 阴影
@shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
@shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
@shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);

// 动画时间
@transition-fast: 0.15s;
@transition-normal: 0.3s;
@transition-slow: 0.5s;

// Z-index层级
@z-index-modal: 1000;
@z-index-overlay: 900;
@z-index-dropdown: 800;
@z-index-header: 700;
