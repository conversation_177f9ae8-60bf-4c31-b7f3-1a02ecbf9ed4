# 时空蛇 (Time Warp Snake)

一个独特的商用级移动端贪吃蛇游戏，具有创新的时空倒流机制和丰富的特色功能。

## 🎮 游戏特色

### 核心创新

- **时空倒流机制** - 消耗能量让蛇身倒退，增加策略性
- **元素系统** - 火、水、电等元素效果，增强视觉体验
- **多种食物类型** - 普通、元素、时空、黄金食物等
- **移动端优化** - 专为触控设计的操作体验

### 技术特点

- 基于 Phaser 3 游戏引擎
- 响应式设计，支持各种屏幕尺寸
- PWA 支持，可安装到主屏幕
- 现代化的粒子效果和动画
- 程序化音效系统
- vite + es6 + JavaScript + less + eslint + prettier

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 📱 移动端优化

- **触控操作**：滑动控制方向，点击使用时空倒流
- **防止缩放**：禁用双指缩放和双击缩放
- **振动反馈**：支持设备振动增强体验
- **PWA 支持**：可添加到主屏幕，离线运行
- **自适应布局**：适配各种屏幕尺寸和方向

## 🎯 游戏玩法

### 基础操作

- 滑动屏幕控制蛇的移动方向
- 点击屏幕使用时空倒流（消耗能量）
- 收集食物让蛇变长并获得分数

### 食物类型

- **普通食物**（红色）：基础分数 +10
- **火元素**（红橙色）：获得火焰效果 +20
- **水元素**（蓝色）：获得水流效果 +20
- **电元素**（黄色）：获得电光效果 +20
- **时空食物**（紫色）：恢复时空能量 +30
- **黄金食物**（金色）：高分奖励和速度提升 +50

### 特殊能力

- **时空倒流**：回到几步之前的位置，避免碰撞
- **元素效果**：不同元素提供视觉特效和轨迹
- **速度提升**：黄金食物提供临时加速

## 🛒 商业化功能

### 皮肤系统

- 经典绿色（免费）
- 火焰蛇（500 金币）
- 水流蛇（500 金币）
- 电光蛇（750 金币）
- 彩虹蛇（1000 金币）
- 银河蛇（1500 金币）

### 道具系统

- 双倍得分
- 时空增强
- 磁性食物
- 护盾保护

### 货币系统

- 游戏内金币
- 观看广告获得奖励
- 内购货币包

## 🎨 技术架构

### 文件结构

```
src/
├── main.js                 # 入口文件
├── scenes/                 # 游戏场景
│   ├── MenuScene.js        # 菜单场景
│   ├── GameScene.js        # 游戏场景
│   └── UIScene.js          # UI界面
├── entities/               # 游戏实体
│   ├── Snake.js            # 蛇类
│   └── Food.js             # 食物类
└── utils/                  # 工具类
    ├── MobileUtils.js      # 移动端工具
    ├── ParticleEffects.js  # 粒子效果
    ├── SoundManager.js     # 音效管理
    └── Monetization.js     # 商业化功能
```

### 核心类说明

#### Snake.js

- 蛇的移动逻辑
- 时空倒流功能
- 元素效果系统
- 碰撞检测

#### Food.js

- 多种食物类型
- 视觉效果
- 奖励系统

#### MobileUtils.js

- 触控手势识别
- 防缩放处理
- PWA 功能
- 设备适配

#### ParticleEffects.js

- 粒子效果系统
- 屏幕特效
- 动画管理

#### SoundManager.js

- 程序化音效生成
- 背景音乐
- 音频设置

#### Monetization.js

- 广告集成框架
- 内购系统
- 皮肤和道具商店
- 游戏货币管理

### 资源加载方式

#### 图片资源

- 当前版本使用 Emoji 作为所有视觉元素的替代方案
- 支持 PNG、JPG、SVG 等常见图片格式
- 推荐使用精灵图集提高性能

#### 音频资源

- 支持 OGG、MP3 等格式（推荐 OGG 优先，MP3 备选）
- 音效文件建议小于 2 秒，单声道，128kbps
- 背景音乐建议 2-5 分钟循环，立体声，192kbps

#### 字体资源

- 支持 WOFF2、WOFF、TTF 等格式
- 推荐使用 WOFF2 格式以获得最佳压缩率
- 可通过 CSS @font-face 或 CDN 方式加载

## 🎨 开源资源推荐

### 游戏风格字体

#### 像素风格字体

- **Press Start 2P**

  - 下载链接: https://fonts.google.com/specimen/Press+Start+2P
  - 许可证: SIL OFL 1.1 (免费商用)
  - 特点: 经典 8 位游戏风格，适合复古游戏

- **Orbitron**

  - 下载链接: https://fonts.google.com/specimen/Orbitron
  - 许可证: SIL OFL 1.1 (免费商用)
  - 特点: 未来科技感，适合科幻主题游戏

- **Bungee**
  - 下载链接: https://fonts.google.com/specimen/Bungee
  - 许可证: SIL OFL 1.1 (免费商用)
  - 特点: 立体效果，适合街机游戏

#### 现代游戏字体

- **Exo 2**

  - 下载链接: https://fonts.google.com/specimen/Exo+2
  - 许可证: SIL OFL 1.1 (免费商用)
  - 特点: 现代几何设计，清晰易读

- **Rajdhani**
  - 下载链接: https://fonts.google.com/specimen/Rajdhani
  - 许可证: SIL OFL 1.1 (免费商用)
  - 特点: 简洁现代，适合 UI 界面

#### 字体使用建议

- 主标题: Press Start 2P 或 Bungee
- 游戏 UI: Orbitron 或 Exo 2
- 分数显示: Rajdhani 或 Orbitron
- 备用字体: system-ui, -apple-system, sans-serif

### 字体图标资源

#### Font Awesome (免费版)

- **下载链接**: https://fontawesome.com/download
- **许可证**: SIL OFL 1.1 (免费商用)
- **特点**: 丰富的图标库，包含播放、暂停、设置等常用游戏 UI 图标

#### Material Design Icons

- **下载链接**: https://materialdesignicons.com/
- **许可证**: Apache 2.0 (免费商用)
- **特点**: Google 设计风格，现代简洁

#### Feather Icons

- **下载链接**: https://feathericons.com/
- **许可证**: MIT (免费商用)
- **特点**: 简洁线条风格，适合现代 UI 设计

### 音频资源

#### 音效资源网站

**Freesound.org**

- **网址**: https://freesound.org/
- **许可证**: Creative Commons (多种许可证可选)
- **推荐搜索关键词**:
  - "game eat sound" - 吃食物音效
  - "button click" - 按钮点击音效
  - "game over" - 游戏结束音效
  - "power up" - 道具获得音效

**OpenGameArt.org**

- **网址**: https://opengameart.org/
- **许可证**: 多种开源许可证
- **分类**: 专门的游戏音效资源
- **推荐标签**: "sound effects", "8-bit", "arcade"

**Zapsplat** (免费账户)

- **网址**: https://www.zapsplat.com/
- **许可证**: 免费账户可商用
- **特点**: 高质量音效，需要注册

#### 背景音乐资源

**Incompetech**

- **网址**: https://incompetech.com/music/royalty-free/
- **许可证**: Creative Commons 4.0
- **推荐曲目类型**: "Video Game", "Electronic"

**Bensound**

- **网址**: https://www.bensound.com/
- **许可证**: Creative Commons (需署名)
- **推荐风格**: "Electronica", "Pop"

#### 音频格式建议

- **优先格式**: OGG Vorbis (文件小，质量高)
- **备选格式**: MP3 (兼容性好)
- **音效规格**: 128kbps，单声道，时长<2 秒
- **音乐规格**: 192kbps，立体声，2-5 分钟循环

## 🍎 临时视觉元素方案

### 食物元素 Emoji 替代

#### 水果类食物

- 🍎 红苹果 - 普通食物
- 🍌 香蕉 - 能量食物
- 🍇 葡萄 - 水元素食物
- 🍓 草莓 - 火元素食物
- 🍊 橙子 - 电元素食物
- 🍉 西瓜 - 大型食物
- 🍍 菠萝 - 黄金食物
- 🍑 桃子 - 时空食物

#### 道具元素 Emoji

- 💎 钻石 - 高价值道具
- ⭐ 星星 - 评级/成就
- 🔥 火焰 - 火元素效果
- ⚡ 闪电 - 电元素效果
- 💧 水滴 - 水元素效果
- 🛡️ 盾牌 - 保护道具
- 🧲 磁铁 - 磁性道具
- ⏰ 时钟 - 时间相关道具
- 🚀 火箭 - 速度提升
- 👑 皇冠 - 特殊奖励

#### UI 界面 Emoji

- ▶️ 播放按钮
- ⏸️ 暂停按钮
- ⏹️ 停止按钮
- ⚙️ 设置按钮
- 🏠 主页按钮
- 🏆 排行榜/成就
- ❤️ 生命值
- 🪙 游戏币
- 🎁 奖励/礼品
- 🎯 目标/任务

### Emoji 使用说明

- 当前版本所有视觉元素都使用 Emoji 替代
- 推荐字体大小: 24px-48px
- 支持动画效果: 缩放、旋转、淡入淡出
- 跨平台兼容性好，无需额外资源文件

## �🔧 自定义配置

### 游戏参数调整

在相关文件中可以调整以下参数：

- 蛇的移动速度
- 时空能量消耗
- 食物生成概率
- 粒子效果强度

### 商业化设置

- 广告显示频率
- 道具价格
- 皮肤解锁条件
- 货币奖励数量

### 资源配置

- **图片路径**: assets/images/ (ui/, game/, effects/)
- **音频路径**: assets/audio/ (sfx/, music/)
- **字体路径**: assets/fonts/
- **音频格式**: 优先 OGG，备选 MP3
- **音量设置**: 音效 0.7，音乐 0.3
- **Emoji 方案**: 当前版本主要使用方式

## 🚀 部署建议

### 生产环境资源优化

- **图片压缩**: 使用 imagemin-cli 等工具压缩 PNG/JPG
- **音频压缩**: 使用 FFmpeg 转换为 OGG 格式
- **字体子集化**: 使用 glyphhanger 减少字体文件大小
- **CDN 部署**: 将静态资源部署到 CDN 加速访问

### CDN 资源配置

- **字体图标**: 使用 CDN 加载 Font Awesome 等图标库
- **Google 字体**: 通过 Google Fonts CDN 加载游戏字体
- **静态资源**: 音频、图片等资源可部署到 CDN

## 🤝 贡献指南

### 资源贡献

- 提交新资源时请包含完整的许可证信息
- 优先使用开源且允许商用的资源
- 提供资源的原始来源链接
- 包含适当的备用方案（如 emoji 替代）

### 代码贡献

- 遵循项目的代码风格
- 添加适当的注释和文档
- 确保新功能有对应的测试
- 更新相关的 README 文档

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题或合作需求，请联系开发团队。

### 技术支持

- 🐛 Bug 报告: 请在 GitHub Issues 中提交
- 💡 功能建议: 欢迎在 Discussions 中讨论
- 📧 商务合作: 请通过邮件联系

### 社区资源

- 📚 开发文档: 详见项目 Wiki
- 🎮 游戏演示: [在线试玩链接]
- 🔧 开发工具: 推荐使用 VS Code + Phaser 插件
