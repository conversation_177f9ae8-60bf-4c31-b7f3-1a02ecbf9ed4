/**
 * 生成PWA图标的Node.js脚本
 * 使用Canvas API生成各种尺寸的PNG图标
 */

import { createCanvas } from 'canvas';
import fs from 'fs';
import path from 'path';

// 需要生成的图标尺寸
const ICON_SIZES = [72, 96, 128, 144, 152, 192, 384, 512];

/**
 * 创建图标Canvas
 */
function createIcon(size) {
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  // 背景渐变
  const gradient = ctx.createLinearGradient(0, 0, size, size);
  gradient.addColorStop(0, '#667eea');
  gradient.addColorStop(1, '#764ba2');
  
  // 绘制圆角背景
  const radius = size * 0.125;
  ctx.fillStyle = gradient;
  ctx.beginPath();
  ctx.roundRect(0, 0, size, size, radius);
  ctx.fill();

  // 计算比例
  const scale = size / 512;
  const gridSize = 40 * scale;
  const startX = 100 * scale;
  const startY = 200 * scale;

  // 绘制蛇身
  ctx.fillStyle = '#4CAF50';
  for (let i = 0; i < 7; i++) {
    const x = startX + i * gridSize;
    const y = startY;
    ctx.fillRect(x, y, gridSize - 2 * scale, gridSize - 2 * scale);
  }

  // 绘制蛇头
  ctx.fillStyle = '#388E3C';
  const headX = startX + 7 * gridSize;
  ctx.fillRect(headX, startY, gridSize - 2 * scale, gridSize - 2 * scale);

  // 绘制眼睛（如果尺寸足够大）
  if (size >= 96) {
    ctx.fillStyle = 'white';
    const eyeSize = 3 * scale;
    ctx.beginPath();
    ctx.arc(headX + 10 * scale, startY + 10 * scale, eyeSize, 0, Math.PI * 2);
    ctx.arc(headX + 30 * scale, startY + 10 * scale, eyeSize, 0, Math.PI * 2);
    ctx.fill();

    ctx.fillStyle = 'black';
    ctx.beginPath();
    ctx.arc(headX + 10 * scale, startY + 10 * scale, eyeSize / 2, 0, Math.PI * 2);
    ctx.arc(headX + 30 * scale, startY + 10 * scale, eyeSize / 2, 0, Math.PI * 2);
    ctx.fill();
  }

  // 绘制食物
  ctx.fillStyle = '#FF5722';
  ctx.beginPath();
  ctx.arc(200 * scale, 120 * scale, 15 * scale, 0, Math.PI * 2);
  ctx.fill();

  // 绘制时空效果（如果尺寸足够大）
  if (size >= 128) {
    const centerX = 256 * scale;
    const centerY = 300 * scale;
    
    ctx.strokeStyle = 'rgba(138, 43, 226, 0.6)';
    ctx.lineWidth = 3 * scale;
    ctx.beginPath();
    ctx.arc(centerX, centerY, 60 * scale, 0, Math.PI * 2);
    ctx.stroke();

    ctx.strokeStyle = 'rgba(138, 43, 226, 0.4)';
    ctx.lineWidth = 2 * scale;
    ctx.beginPath();
    ctx.arc(centerX, centerY, 40 * scale, 0, Math.PI * 2);
    ctx.stroke();
  }

  // 添加文字（如果尺寸足够大）
  if (size >= 192) {
    ctx.fillStyle = 'white';
    ctx.font = `bold ${36 * scale}px Arial, sans-serif`;
    ctx.textAlign = 'center';
    ctx.fillText('时空蛇', 256 * scale, 400 * scale);
    
    ctx.font = `${16 * scale}px Arial, sans-serif`;
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.fillText('Time Warp Snake', 256 * scale, 430 * scale);
  }

  return canvas;
}

/**
 * 生成所有图标
 */
async function generateIcons() {
  const assetsDir = path.join(process.cwd(), 'public', 'assets');
  
  // 确保assets目录存在
  if (!fs.existsSync(assetsDir)) {
    fs.mkdirSync(assetsDir, { recursive: true });
  }

  console.log('开始生成图标...');

  for (const size of ICON_SIZES) {
    try {
      const canvas = createIcon(size);
      const buffer = canvas.toBuffer('image/png');
      const filename = path.join(assetsDir, `icon-${size}.png`);
      
      fs.writeFileSync(filename, buffer);
      console.log(`✅ 生成图标: icon-${size}.png`);
    } catch (error) {
      console.error(`❌ 生成 ${size}x${size} 图标失败:`, error.message);
    }
  }

  console.log('图标生成完成！');
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  generateIcons().catch(console.error);
}

export { generateIcons, createIcon };
