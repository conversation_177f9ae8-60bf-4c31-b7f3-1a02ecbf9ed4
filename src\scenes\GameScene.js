import Phaser from 'phaser';

/**
 * 主游戏场景
 * 游戏的核心玩法场景
 */
export class GameScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameScene' });
  }

  create() {
    console.log('GameScene: 游戏场景启动');

    const width = this.cameras.main.width;
    const height = this.cameras.main.height;

    // 临时显示文本
    this.add
      .text(width / 2, height / 2 - 50, '游戏场景', {
        fontSize: '32px',
        fill: '#ffffff',
      })
      .setOrigin(0.5);

    this.add
      .text(width / 2, height / 2, '(开发中...)', {
        fontSize: '18px',
        fill: '#888888',
      })
      .setOrigin(0.5);

    // 返回菜单按钮
    const backButton = this.add
      .text(width / 2, height / 2 + 50, '返回菜单', {
        fontSize: '20px',
        fill: '#ffffff',
        backgroundColor: '#666666',
        padding: { x: 20, y: 10 },
      })
      .setOrigin(0.5)
      .setInteractive();

    backButton.on('pointerdown', () => {
      this.scene.start('MenuScene');
    });
  }
}
