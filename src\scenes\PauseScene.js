import Phaser from 'phaser';

/**
 * 暂停场景
 * 游戏暂停时的界面
 */
export class PauseScene extends Phaser.Scene {
  constructor() {
    super({ key: 'PauseScene' });
  }

  create() {
    console.log('PauseScene: 暂停场景启动');

    const width = this.cameras.main.width;
    const height = this.cameras.main.height;

    // 半透明背景
    this.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.7);

    // 暂停文本
    this.add
      .text(width / 2, height / 2 - 50, '游戏暂停', {
        fontSize: '32px',
        fill: '#ffffff',
      })
      .setOrigin(0.5);

    // 继续游戏按钮
    const resumeButton = this.add
      .text(width / 2, height / 2 + 20, '继续游戏', {
        fontSize: '20px',
        fill: '#ffffff',
        backgroundColor: '#4CAF50',
        padding: { x: 20, y: 10 },
      })
      .setOrigin(0.5)
      .setInteractive();

    resumeButton.on('pointerdown', () => {
      this.scene.resume('GameScene');
      this.scene.stop();
    });

    // 返回菜单按钮
    const menuButton = this.add
      .text(width / 2, height / 2 + 80, '返回菜单', {
        fontSize: '20px',
        fill: '#ffffff',
        backgroundColor: '#666666',
        padding: { x: 20, y: 10 },
      })
      .setOrigin(0.5)
      .setInteractive();

    menuButton.on('pointerdown', () => {
      this.scene.stop('GameScene');
      this.scene.start('MenuScene');
    });
  }
}
