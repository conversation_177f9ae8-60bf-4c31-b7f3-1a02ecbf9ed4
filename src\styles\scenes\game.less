// 游戏场景样式
.game-scene {
  position: relative;
  width: 100%;
  height: 100%;
  background: @bg-primary;
  overflow: hidden;

  // 游戏画布容器
  .game-canvas-container {
    position: relative;
    width: 100%;
    height: 100%;
    .center-flex();

    canvas {
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: @border-radius;
      box-shadow: @shadow-lg;
      background: #1a1a1a;
    }
  }
}

// 游戏UI覆盖层 - 独立样式，不依赖父容器
.game-ui {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: @z-index-overlay;

  > * {
    pointer-events: auto;
  }
}

// 顶部状态栏
.game-ui .top-bar {
  position: absolute;
  top: @spacing-md;
  left: @spacing-md;
  right: @spacing-md;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.game-ui .top-bar .score-panel {
  background: rgba(0, 0, 0, 0.7);
  color: @text-light;
  padding: @spacing-sm @spacing-md;
  border-radius: @border-radius;
  backdrop-filter: blur(10px);
}

.game-ui .top-bar .score-panel .score-label {
  font-size: @font-size-xs;
  opacity: 0.8;
  margin-bottom: 2px;
}

.game-ui .top-bar .score-panel .score-value {
  font-size: @font-size-lg;
  font-weight: bold;
  color: @accent-color;
}

.game-ui .top-bar .energy-panel {
  background: rgba(0, 0, 0, 0.7);
  padding: @spacing-sm @spacing-md;
  border-radius: @border-radius;
  backdrop-filter: blur(10px);
  min-width: 120px;
}

.game-ui .top-bar .energy-panel .energy-label {
  font-size: @font-size-xs;
  color: @text-light;
  opacity: 0.8;
  margin-bottom: 4px;
  text-align: center;
}

.game-ui .top-bar .energy-panel .energy-bar-container {
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.game-ui .top-bar .energy-panel .energy-bar-container .energy-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, @snake-timewarp, lighten(@snake-timewarp, 20%));
  transition: width @transition-normal ease;
  position: relative;
}

.game-ui .top-bar .energy-panel .energy-bar-container .energy-bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: energyShimmer 2s infinite;
}

.game-ui .top-bar .energy-panel .energy-text {
  font-size: @font-size-xs;
  color: @text-light;
  text-align: center;
  margin-top: 2px;
}

// 底部控制区
.game-ui .bottom-controls {
  position: absolute;
  bottom: @spacing-md;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: @spacing-md;
  align-items: center;
}

.game-ui .bottom-controls .control-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: @text-light;
  border: 2px solid rgba(255, 255, 255, 0.3);
  .center-flex();
  font-size: @font-size-lg;
  cursor: pointer;
  transition: all @transition-normal ease;
  backdrop-filter: blur(10px);
}

.game-ui .bottom-controls .control-button:hover {
  background: rgba(0, 0, 0, 0.8);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.game-ui .bottom-controls .control-button:active {
  transform: scale(0.95);
}

.game-ui .bottom-controls .control-button.pause-btn {
  background: rgba(255, 193, 7, 0.8);
  border-color: @warning-color;
}

.game-ui .bottom-controls .control-button.pause-btn:hover {
  background: @warning-color;
}

.game-ui .bottom-controls .control-button.timewarp-btn {
  background: linear-gradient(135deg, @snake-timewarp, lighten(@snake-timewarp, 20%));
  border-color: @snake-timewarp;
  position: relative;
}

.game-ui .bottom-controls .control-button.timewarp-btn:disabled {
  background: rgba(0, 0, 0, 0.5);
  border-color: rgba(255, 255, 255, 0.2);
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.game-ui .bottom-controls .control-button.timewarp-btn::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid @snake-timewarp;
  border-radius: 50%;
  opacity: 0;
  animation: timewarpPulse 2s infinite;
}

.game-ui .bottom-controls .control-button.timewarp-btn:not(:disabled)::before {
  opacity: 1;
}

// 游戏提示
.game-ui .game-hints {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: @text-light;
  font-size: @font-size-sm;
  opacity: 0.7;
}

.game-ui .game-hints .hint-text {
  margin-bottom: @spacing-xs;
  animation: hintFade 3s infinite;
}

.game-ui .game-hints.mobile-hints {
  .mobile-only({
    display: block;
  });

  .tablet-up({
    display: none;
  });
}
// 特效覆盖层
.game-ui .effects-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: @z-index-dropdown;
}

.game-ui .effects-overlay .timewarp-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 3px solid @snake-timewarp;
  border-radius: 50%;
  opacity: 0;
  animation: timewarpRipple 1s ease-out;
}

.game-ui .effects-overlay .element-trail {
  position: absolute;
  pointer-events: none;
}

.game-ui .effects-overlay .element-trail.fire-trail {
  background: radial-gradient(circle, @snake-fire, transparent);
}

.game-ui .effects-overlay .element-trail.water-trail {
  background: radial-gradient(circle, @snake-water, transparent);
}

.game-ui .effects-overlay .element-trail.electric-trail {
  background: radial-gradient(circle, @snake-electric, transparent);
}

// 动画
@keyframes energyShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes timewarpPulse {
  0%, 100% { 
    opacity: 0;
    transform: scale(1);
  }
  50% { 
    opacity: 0.6;
    transform: scale(1.2);
  }
}

@keyframes timewarpRipple {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.5);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}

@keyframes hintFade {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.3; }
}
  